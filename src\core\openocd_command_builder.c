#include "openocd_command_builder.h"
#include "logger.h"
#include <string.h>
#include <stdio.h>

// OpenOCD command format templates
static const char* WRITE_IMAGE_TEMPLATES[] = {
    "flash write_image erase \"%s\" 0x%08x %s",     // Binary with address
    "flash write_image erase \"%s\" %s",            // Other formats
    "flash write_image erase \"%s\" 0x%08x",        // Binary without type
    "flash write_image erase \"%s\"",               // Auto-detect format
    "flash write_image \"%s\" 0x%08x %s",           // No erase, binary
    "flash write_image \"%s\" %s",                  // No erase, other
    NULL
};

static const char* VERIFY_IMAGE_TEMPLATES[] = {
    "verify_image \"%s\" 0x%08x %s",                // Binary with address
    "verify_image \"%s\" %s",                       // Other formats
    "verify_image \"%s\" 0x%08x",                   // Binary without type
    "verify_image \"%s\"",                          // Auto-detect format
    NULL
};

// OpenOCD version information
typedef struct {
    int major;
    int minor;
    int patch;
    char version_string[64];
    bool supports_quad_en;
    bool supports_unlock;
} openocd_version_info_t;

static openocd_version_info_t g_openocd_version = {0};
static bool g_version_detected = false;

/**
 * Parse OpenOCD version string
 */
static bool parse_openocd_version(const char* version_str, openocd_version_info_t* info) {
    if (!version_str || !info) {
        return false;
    }
    
    // Copy version string
    strncpy(info->version_string, version_str, sizeof(info->version_string) - 1);
    info->version_string[sizeof(info->version_string) - 1] = '\0';
    
    // Parse version numbers (format: "Open On-Chip Debugger 0.11.0")
    const char* version_start = strstr(version_str, "0.");
    if (!version_start) {
        version_start = strstr(version_str, "1.");
    }
    
    if (version_start) {
        if (sscanf(version_start, "%d.%d.%d", &info->major, &info->minor, &info->patch) >= 2) {
            // Set feature support based on version
            if (info->major > 0 || (info->major == 0 && info->minor >= 11)) {
                info->supports_quad_en = true;
                info->supports_unlock = true;
            }
            return true;
        }
    }
    
    // Default to basic support if version parsing fails
    info->major = 0;
    info->minor = 10;
    info->patch = 0;
    info->supports_quad_en = false;
    info->supports_unlock = true;
    
    return false;
}

/**
 * Detect OpenOCD version and capabilities
 */
jtag_error_t openocd_command_detect_version(openocd_client_t* client) {
    if (!client) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char response[MAX_RESPONSE_LENGTH];
    jtag_error_t ret = openocd_client_send_command(client, "version", response, sizeof(response));
    
    if (ret == JTAG_SUCCESS) {
        parse_openocd_version(response, &g_openocd_version);
        g_version_detected = true;
        
        LOG_INFO("Detected OpenOCD version: %s", g_openocd_version.version_string);
        LOG_DEBUG("Version: %d.%d.%d, quad_en: %s, unlock: %s",
                 g_openocd_version.major, g_openocd_version.minor, g_openocd_version.patch,
                 g_openocd_version.supports_quad_en ? "yes" : "no",
                 g_openocd_version.supports_unlock ? "yes" : "no");
    } else {
        LOG_WARN("Failed to detect OpenOCD version, using defaults");
        // Use default version info
        g_openocd_version.major = 0;
        g_openocd_version.minor = 10;
        g_openocd_version.patch = 0;
        g_openocd_version.supports_quad_en = false;
        g_openocd_version.supports_unlock = true;
        strcpy(g_openocd_version.version_string, "Unknown");
        g_version_detected = true;
    }
    
    return ret;
}

/**
 * Build flash write_image command
 */
jtag_error_t openocd_command_build_write_image(const flash_config_t* config,
                                              const openocd_command_options_t* options,
                                              char* command,
                                              size_t command_size) {
    if (!config || !command) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    // Ensure version is detected
    if (!g_version_detected) {
        LOG_WARN("OpenOCD version not detected, using default command format");
    }
    
    // Build command components
    char base_cmd[64] = "flash write_image";
    char options_str[128] = "";
    char file_part[512];
    char address_part[32] = "";
    char type_part[16] = "";
    
    // Add options
    if (!options || options->erase) {
        strcat(options_str, " erase");
    }
    
    if (options && options->unlock && g_openocd_version.supports_unlock) {
        strcat(options_str, " unlock");
    }
    
    if (options && options->quad_en && g_openocd_version.supports_quad_en) {
        strcat(options_str, " quad_en");
    }
    
    // File path (with quotes for safety)
    snprintf(file_part, sizeof(file_part), " \"%s\"", config->firmware_file);
    
    // Address for binary files
    if (config->file_type == FIRMWARE_TYPE_BIN) {
        snprintf(address_part, sizeof(address_part), " 0x%08x", config->base_address);
    }
    
    // File type specification
    const char* file_type = get_file_type_string(config->file_type);
    if (file_type && strlen(file_type) > 0) {
        snprintf(type_part, sizeof(type_part), " %s", file_type);
    }
    
    // Construct final command
    int result = snprintf(command, command_size, "%s%s%s%s%s",
                         base_cmd, options_str, file_part, address_part, type_part);
    
    if (result >= command_size) {
        LOG_ERROR("Command buffer too small");
        return JTAG_ERROR_BUFFER_TOO_SMALL;
    }
    
    LOG_DEBUG("Built write command: %s", command);
    return JTAG_SUCCESS;
}

/**
 * Build verify_image command
 */
jtag_error_t openocd_command_build_verify_image(const flash_config_t* config,
                                               char* command,
                                               size_t command_size) {
    if (!config || !command) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char file_part[512];
    char address_part[32] = "";
    char type_part[16] = "";
    
    // File path (with quotes for safety)
    snprintf(file_part, sizeof(file_part), "\"%s\"", config->firmware_file);
    
    // Address for binary files
    if (config->file_type == FIRMWARE_TYPE_BIN) {
        snprintf(address_part, sizeof(address_part), " 0x%08x", config->base_address);
    }
    
    // File type specification
    const char* file_type = get_file_type_string(config->file_type);
    if (file_type && strlen(file_type) > 0) {
        snprintf(type_part, sizeof(type_part), " %s", file_type);
    }
    
    // Construct final command
    int result = snprintf(command, command_size, "verify_image %s%s%s",
                         file_part, address_part, type_part);
    
    if (result >= command_size) {
        LOG_ERROR("Command buffer too small");
        return JTAG_ERROR_BUFFER_TOO_SMALL;
    }
    
    LOG_DEBUG("Built verify command: %s", command);
    return JTAG_SUCCESS;
}

/**
 * Test command format and adapt if needed
 */
jtag_error_t openocd_command_test_and_adapt(openocd_client_t* client,
                                           const char* test_command,
                                           char* adapted_command,
                                           size_t command_size) {
    if (!client || !test_command || !adapted_command) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char response[MAX_RESPONSE_LENGTH];
    
    // Try the command
    jtag_error_t ret = openocd_client_send_command(client, test_command, response, sizeof(response));
    
    if (ret == JTAG_SUCCESS) {
        // Check for syntax errors in response
        if (strstr(response, "syntax error") || 
            strstr(response, "invalid command") ||
            strstr(response, "unknown command")) {
            
            LOG_WARN("Command format not supported: %s", test_command);
            LOG_WARN("OpenOCD response: %s", response);
            
            // TODO: Implement fallback command generation
            // For now, just copy the original command
            strncpy(adapted_command, test_command, command_size - 1);
            adapted_command[command_size - 1] = '\0';
            
            return JTAG_ERROR_COMMAND_SYNTAX;
        } else {
            // Command format is valid
            strncpy(adapted_command, test_command, command_size - 1);
            adapted_command[command_size - 1] = '\0';
            return JTAG_SUCCESS;
        }
    }
    
    return ret;
}

/**
 * Get OpenOCD version information
 */
const openocd_version_info_t* openocd_command_get_version_info(void) {
    return g_version_detected ? &g_openocd_version : NULL;
}

/**
 * Reset version detection (for testing)
 */
void openocd_command_reset_version_detection(void) {
    g_version_detected = false;
    memset(&g_openocd_version, 0, sizeof(g_openocd_version));
}
