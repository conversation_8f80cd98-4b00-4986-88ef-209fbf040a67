#include "storage_interface.h"
#include "logger.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

// 命令模板结构
typedef struct command_template {
    const char* operation;      // write, read, erase, verify, configure
    storage_type_t storage_type;
    file_format_t file_format;
    
    // 命令模板字符串
    const char* template_str;
    
    // 版本兼容性
    int min_major_version;
    int min_minor_version;
    
    // 特性要求
    bool requires_address;
    bool supports_erase_option;
    bool supports_unlock_option;
    bool supports_quad_en;
    
} command_template_t;

// ============================================================================
// Flash命令模板
// ============================================================================

static const command_template_t flash_templates[] = {
    // Flash write operations
    {
        .operation = "write",
        .storage_type = STORAGE_TYPE_FLASH_NOR,
        .file_format = FILE_FORMAT_BIN,
        .template_str = "flash write_image erase \"{filename}\" 0x{address} bin",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = true,
        .supports_erase_option = true,
        .supports_unlock_option = true,
        .supports_quad_en = false
    },
    {
        .operation = "write",
        .storage_type = STORAGE_TYPE_FLASH_NOR,
        .file_format = FILE_FORMAT_HEX,
        .template_str = "flash write_image erase \"{filename}\"",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = false,
        .supports_erase_option = true,
        .supports_unlock_option = true,
        .supports_quad_en = false
    },
    {
        .operation = "write",
        .storage_type = STORAGE_TYPE_FLASH_NOR,
        .file_format = FILE_FORMAT_ELF,
        .template_str = "flash write_image erase \"{filename}\"",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = false,
        .supports_erase_option = true,
        .supports_unlock_option = true,
        .supports_quad_en = false
    },
    
    // Flash verify operations
    {
        .operation = "verify",
        .storage_type = STORAGE_TYPE_FLASH_NOR,
        .file_format = FILE_FORMAT_BIN,
        .template_str = "verify_image \"{filename}\" 0x{address} bin",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = true,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    },
    {
        .operation = "verify",
        .storage_type = STORAGE_TYPE_FLASH_NOR,
        .file_format = FILE_FORMAT_HEX,
        .template_str = "verify_image \"{filename}\"",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = false,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    },
    
    // Flash erase operations
    {
        .operation = "erase",
        .storage_type = STORAGE_TYPE_FLASH_NOR,
        .file_format = FILE_FORMAT_AUTO,
        .template_str = "flash erase_address 0x{address} 0x{length}",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = true,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    },
    
    // Flash read operations
    {
        .operation = "read",
        .storage_type = STORAGE_TYPE_FLASH_NOR,
        .file_format = FILE_FORMAT_BIN,
        .template_str = "flash read_bank {bank_id} \"{filename}\" 0x{address} 0x{length}",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = true,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    }
};

// ============================================================================
// NAND命令模板
// ============================================================================

static const command_template_t nand_templates[] = {
    // NAND write operations
    {
        .operation = "write",
        .storage_type = STORAGE_TYPE_FLASH_NAND,
        .file_format = FILE_FORMAT_BIN,
        .template_str = "nand write {device_id} \"{filename}\" 0x{offset}",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = true,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    },
    {
        .operation = "write",
        .storage_type = STORAGE_TYPE_FLASH_NAND,
        .file_format = FILE_FORMAT_BIT,
        .template_str = "nand write {device_id} \"{filename}\" 0x{offset}",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = true,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    },
    
    // NAND erase operations
    {
        .operation = "erase",
        .storage_type = STORAGE_TYPE_FLASH_NAND,
        .file_format = FILE_FORMAT_AUTO,
        .template_str = "nand erase {device_id} 0x{offset} 0x{length}",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = true,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    },
    
    // NAND read operations
    {
        .operation = "read",
        .storage_type = STORAGE_TYPE_FLASH_NAND,
        .file_format = FILE_FORMAT_BIN,
        .template_str = "nand dump {device_id} \"{filename}\" 0x{offset} 0x{length}",
        .min_major_version = 0,
        .min_minor_version = 10,
        .requires_address = true,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    }
};

// ============================================================================
// eMMC命令模板
// ============================================================================

static const command_template_t emmc_templates[] = {
    // eMMC write operations
    {
        .operation = "write",
        .storage_type = STORAGE_TYPE_EMMC,
        .file_format = FILE_FORMAT_BIN,
        .template_str = "emmc write {device_id} {partition} \"{filename}\" 0x{offset}",
        .min_major_version = 0,
        .min_minor_version = 11,
        .requires_address = true,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    },
    
    // eMMC read operations
    {
        .operation = "read",
        .storage_type = STORAGE_TYPE_EMMC,
        .file_format = FILE_FORMAT_BIN,
        .template_str = "emmc read {device_id} {partition} \"{filename}\" 0x{offset} 0x{length}",
        .min_major_version = 0,
        .min_minor_version = 11,
        .requires_address = true,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    }
};

// ============================================================================
// FPGA命令模板
// ============================================================================

static const command_template_t fpga_templates[] = {
    // FPGA configure operations
    {
        .operation = "configure",
        .storage_type = STORAGE_TYPE_FPGA,
        .file_format = FILE_FORMAT_BIT,
        .template_str = "fpga configure \"{filename}\"",
        .min_major_version = 0,
        .min_minor_version = 12,
        .requires_address = false,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    },
    {
        .operation = "configure",
        .storage_type = STORAGE_TYPE_FPGA,
        .file_format = FILE_FORMAT_RBF,
        .template_str = "fpga configure \"{filename}\" rbf",
        .min_major_version = 0,
        .min_minor_version = 12,
        .requires_address = false,
        .supports_erase_option = false,
        .supports_unlock_option = false,
        .supports_quad_en = false
    }
};

// ============================================================================
// 命令模板管理器
// ============================================================================

// 查找命令模板
static const command_template_t* find_template(const char* operation,
                                              storage_type_t storage_type,
                                              file_format_t file_format) {
    const command_template_t* templates = NULL;
    size_t template_count = 0;
    
    // 根据存储类型选择模板数组
    switch (storage_type) {
        case STORAGE_TYPE_FLASH_NOR:
            templates = flash_templates;
            template_count = sizeof(flash_templates) / sizeof(flash_templates[0]);
            break;
        case STORAGE_TYPE_FLASH_NAND:
            templates = nand_templates;
            template_count = sizeof(nand_templates) / sizeof(nand_templates[0]);
            break;
        case STORAGE_TYPE_EMMC:
            templates = emmc_templates;
            template_count = sizeof(emmc_templates) / sizeof(emmc_templates[0]);
            break;
        case STORAGE_TYPE_FPGA:
            templates = fpga_templates;
            template_count = sizeof(fpga_templates) / sizeof(fpga_templates[0]);
            break;
        default:
            return NULL;
    }
    
    // 查找匹配的模板
    for (size_t i = 0; i < template_count; i++) {
        if (strcmp(templates[i].operation, operation) == 0 &&
            templates[i].storage_type == storage_type &&
            (templates[i].file_format == file_format || 
             templates[i].file_format == FILE_FORMAT_AUTO)) {
            return &templates[i];
        }
    }
    
    return NULL;
}

// 替换模板中的参数
static jtag_error_t substitute_parameters(const char* template_str,
                                         const operation_params_t* params,
                                         char* output,
                                         size_t output_size) {
    if (!template_str || !params || !output) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char temp[MAX_COMMAND_LENGTH];
    strncpy(temp, template_str, sizeof(temp) - 1);
    temp[sizeof(temp) - 1] = '\0';
    
    // 替换文件名
    char* pos = strstr(temp, "{filename}");
    if (pos && params->filename) {
        char before[MAX_COMMAND_LENGTH];
        char after[MAX_COMMAND_LENGTH];
        
        *pos = '\0';
        strcpy(before, temp);
        strcpy(after, pos + strlen("{filename}"));
        
        snprintf(temp, sizeof(temp), "%s%s%s", before, params->filename, after);
    }
    
    // 替换地址
    pos = strstr(temp, "{address}");
    if (pos) {
        char before[MAX_COMMAND_LENGTH];
        char after[MAX_COMMAND_LENGTH];
        
        *pos = '\0';
        strcpy(before, temp);
        strcpy(after, pos + strlen("{address}"));
        
        snprintf(temp, sizeof(temp), "%s0x%08x%s", before, params->address, after);
    }
    
    // 替换偏移量
    pos = strstr(temp, "{offset}");
    if (pos) {
        char before[MAX_COMMAND_LENGTH];
        char after[MAX_COMMAND_LENGTH];
        
        *pos = '\0';
        strcpy(before, temp);
        strcpy(after, pos + strlen("{offset}"));
        
        snprintf(temp, sizeof(temp), "%s0x%08x%s", before, params->address, after);
    }
    
    // 替换长度
    pos = strstr(temp, "{length}");
    if (pos) {
        char before[MAX_COMMAND_LENGTH];
        char after[MAX_COMMAND_LENGTH];
        
        *pos = '\0';
        strcpy(before, temp);
        strcpy(after, pos + strlen("{length}"));
        
        snprintf(temp, sizeof(temp), "%s0x%08x%s", before, params->length, after);
    }
    
    // 复制结果
    strncpy(output, temp, output_size - 1);
    output[output_size - 1] = '\0';
    
    return JTAG_SUCCESS;
}

// ============================================================================
// 公共接口
// ============================================================================

jtag_error_t command_template_build(const char* operation,
                                   storage_type_t storage_type,
                                   const operation_params_t* params,
                                   char* command,
                                   size_t command_size) {
    if (!operation || !params || !command) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    // 查找模板
    const command_template_t* template = find_template(operation, storage_type, params->format);
    if (!template) {
        LOG_ERROR("No command template found for operation=%s, storage=%d, format=%d",
                 operation, storage_type, params->format);
        return JTAG_ERROR_NOT_FOUND;
    }
    
    // 替换参数
    jtag_error_t ret = substitute_parameters(template->template_str, params, command, command_size);
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Failed to substitute parameters in template");
        return ret;
    }
    
    LOG_DEBUG("Built command from template: %s", command);
    return JTAG_SUCCESS;
}

bool command_template_is_supported(const char* operation,
                                  storage_type_t storage_type,
                                  file_format_t file_format,
                                  int openocd_major,
                                  int openocd_minor) {
    const command_template_t* template = find_template(operation, storage_type, file_format);
    if (!template) {
        return false;
    }
    
    // 检查版本兼容性
    if (openocd_major > template->min_major_version ||
        (openocd_major == template->min_major_version && 
         openocd_minor >= template->min_minor_version)) {
        return true;
    }
    
    return false;
}
