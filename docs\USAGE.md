# JTAG Writer 使用说明

## 概述

JTAG Writer 是一个基于 OpenOCD 的固件烧写工具，提供了简单易用的命令行界面来执行固件烧写、验证、擦除等操作。

## 安装和配置

### 前置条件

1. 安装 OpenOCD
2. 确保目标硬件连接正确
3. 配置 OpenOCD 配置文件

### 编译

```bash
mkdir build
cd build
cmake ..
make
```

## 基本用法

### 烧写固件

```bash
# 烧写二进制文件（需要指定地址）
./jtag_writer -f firmware.bin -a 0x08000000

# 烧写HEX文件
./jtag_writer -f firmware.hex

# 烧写ELF文件
./jtag_writer -f firmware.elf
```

### 验证固件

```bash
# 验证已烧写的固件
./jtag_writer --verify -f firmware.bin
```

### 擦除Flash

```bash
# 擦除整个Flash
./jtag_writer --erase
```

### 读取Flash

```bash
# 读取Flash内容到文件
./jtag_writer --read
```

## 命令行选项

### 基本选项

- `-h, --help`: 显示帮助信息
- `-v, --version`: 显示版本信息
- `-V, --verbose`: 详细输出模式

### 文件选项

- `-f, --file <文件>`: 指定固件文件
- `-a, --address <地址>`: 指定基地址（用于二进制文件）
- `-T, --type <类型>`: 指定文件类型 (bin/hex/elf/s19)

### 连接选项

- `-H, --host <主机>`: 指定OpenOCD主机地址
- `-p, --port <端口>`: 指定OpenOCD TCL端口
- `-P, --telnet-port <端口>`: 指定OpenOCD Telnet端口

### 操作模式

- `--erase`: 仅擦除Flash
- `--verify`: 仅验证固件
- `--read`: 读取Flash内容
- `-m, --mode <模式>`: 指定操作模式 (write/verify/erase/read)

### 控制选项

- `--no-erase`: 写入前不擦除
- `--no-verify`: 写入后不验证
- `--no-reset`: 写入后不重置

### 配置和日志

- `-c, --config <文件>`: 指定配置文件
- `-l, --log-level <级别>`: 指定日志级别 (error/warn/info/debug)

## 目标配置

目标类型配置现在由OpenOCD处理。在启动OpenOCD时指定正确的目标配置文件：

```bash
# STM32F1系列示例
openocd -f interface/stlink.cfg -f target/stm32f1x.cfg

# STM32F4系列示例
openocd -f interface/stlink.cfg -f target/stm32f4x.cfg
```

## 支持的文件格式

- `bin`: 二进制文件（需要指定地址）
- `hex`: Intel HEX文件
- `elf`: ELF可执行文件
- `s19`: Motorola S-record文件
- `auto`: 自动检测（根据文件扩展名）

## 配置文件

可以使用配置文件来设置默认参数：

```ini
# OpenOCD配置
openocd_host=localhost
openocd_tcl_port=6666
openocd_telnet_port=4444

# Flash配置
firmware_file=firmware.bin
base_address=0x08000000

# 日志配置
log_level=info
```

使用配置文件：

```bash
./jtag_writer -c config.conf
```

## 示例

### 基本示例

```bash
# 基本烧写
./jtag_writer -f app.bin -a 0x08000000

# 使用HEX文件
./jtag_writer -f app.hex

# 详细模式，不重置
./jtag_writer -V --no-reset -f app.bin -a 0x08000000
```

### 远程OpenOCD服务器

```bash
# 连接到远程服务器
./jtag_writer -H ************* -p 6666 -f firmware.bin
```

### 批量操作

```bash
# 擦除、烧写、验证
./jtag_writer --erase
./jtag_writer -f firmware.bin --no-erase
./jtag_writer --verify -f firmware.bin
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查OpenOCD是否运行
   - 检查端口是否正确
   - 检查防火墙设置

2. **烧写失败**
   - 检查OpenOCD目标配置是否正确
   - 检查固件文件是否存在
   - 检查地址是否正确

3. **验证失败**
   - 检查固件文件是否与Flash内容匹配
   - 检查地址偏移

### 调试模式

使用详细模式和调试日志级别：

```bash
./jtag_writer -V -l debug -f firmware.bin
```

## 注意事项

1. 确保OpenOCD正确配置并运行
2. 烧写前备份重要数据
3. 在OpenOCD配置中选择正确的目标类型
4. 对于二进制文件，必须指定正确的基地址
5. 某些操作可能需要目标处于特定状态（如halt）

## OpenOCD命令格式参考

详细的OpenOCD命令格式请参考 `docs/OPENOCD_COMMANDS.md` 文档。

## 更多信息

- 查看 `examples/` 目录中的示例配置
- 参考 OpenOCD 官方文档了解目标配置选项
- 参考 `docs/OPENOCD_COMMANDS.md` 了解支持的OpenOCD命令格式
- 使用 `--help` 查看完整的命令行选项
