# 可扩展的存储介质和文件格式架构设计

## 概述

为了支持未来的NAND、eMMC、bit文件等多种存储介质和文件格式，我们需要设计一个模块化、可扩展的架构。

## 架构设计原则

### 1. 分层架构
```
应用层 (CLI/GUI)
    ↓
业务逻辑层 (Flash Operations)
    ↓
命令构建层 (Command Builder)
    ↓
存储介质抽象层 (Storage Abstraction)
    ↓
OpenOCD通信层 (OpenOCD Client)
```

### 2. 模块化设计
- **存储介质模块**: Flash, NAND, eMMC, FPGA等
- **文件格式模块**: BIN, HEX, ELF, S19, BIT等
- **命令模板模块**: 可配置的命令格式模板
- **版本适配模块**: OpenOCD版本兼容性处理

### 3. 插件化扩展
- 新的存储介质可以作为插件添加
- 新的文件格式可以独立实现
- 命令格式可以通过配置文件定义

## 核心组件设计

### 1. 存储介质抽象层

```c
// 存储介质类型枚举
typedef enum {
    STORAGE_TYPE_FLASH_NOR,     // NOR Flash
    STORAGE_TYPE_FLASH_NAND,    // NAND Flash  
    STORAGE_TYPE_EMMC,          // eMMC
    STORAGE_TYPE_FPGA,          // FPGA配置
    STORAGE_TYPE_UNKNOWN
} storage_type_t;

// 存储介质接口
typedef struct storage_interface {
    storage_type_t type;
    const char* name;
    
    // 操作函数指针
    jtag_error_t (*init)(void* context);
    jtag_error_t (*write)(void* context, const write_params_t* params);
    jtag_error_t (*read)(void* context, const read_params_t* params);
    jtag_error_t (*erase)(void* context, const erase_params_t* params);
    jtag_error_t (*verify)(void* context, const verify_params_t* params);
    
    // 命令构建函数
    jtag_error_t (*build_write_cmd)(const void* config, char* cmd, size_t size);
    jtag_error_t (*build_read_cmd)(const void* config, char* cmd, size_t size);
    jtag_error_t (*build_erase_cmd)(const void* config, char* cmd, size_t size);
    
    // 特性支持
    bool supports_erase;
    bool supports_verify;
    bool supports_partial_write;
    
} storage_interface_t;
```

### 2. 文件格式处理器

```c
// 文件格式类型
typedef enum {
    FILE_FORMAT_BIN,    // 二进制文件
    FILE_FORMAT_HEX,    // Intel HEX
    FILE_FORMAT_ELF,    // ELF可执行文件
    FILE_FORMAT_S19,    // Motorola S-record
    FILE_FORMAT_BIT,    // Xilinx位流文件
    FILE_FORMAT_RBF,    // Altera Raw Binary File
    FILE_FORMAT_AUTO    // 自动检测
} file_format_t;

// 文件格式处理器接口
typedef struct file_format_handler {
    file_format_t format;
    const char* name;
    const char** extensions;  // 支持的文件扩展名
    
    // 文件处理函数
    jtag_error_t (*detect)(const char* filename);
    jtag_error_t (*validate)(const char* filename);
    jtag_error_t (*get_info)(const char* filename, file_info_t* info);
    
    // 地址处理
    bool requires_address;    // 是否需要指定地址
    bool has_embedded_address; // 是否包含地址信息
    
    // OpenOCD参数
    const char* openocd_type_name;
    
} file_format_handler_t;
```

### 3. 命令模板系统

```c
// 命令模板结构
typedef struct command_template {
    const char* operation;      // write, read, erase, verify
    storage_type_t storage_type;
    file_format_t file_format;
    
    // 命令模板字符串
    const char* template_str;
    
    // 参数映射
    const char** required_params;
    const char** optional_params;
    
    // 版本兼容性
    int min_major_version;
    int min_minor_version;
    
} command_template_t;
```

## 具体实现方案

### 1. Flash存储介质实现

```c
// Flash特定配置
typedef struct flash_config {
    uint32_t base_address;
    uint32_t size;
    uint32_t sector_size;
    bool auto_erase;
    bool auto_verify;
} flash_config_t;

// Flash接口实现
static jtag_error_t flash_build_write_cmd(const void* config, char* cmd, size_t size) {
    const flash_config_t* flash_cfg = (const flash_config_t*)config;
    // 根据文件格式构建不同的命令
    // ...
}
```

### 2. NAND存储介质实现

```c
// NAND特定配置
typedef struct nand_config {
    uint32_t device_id;
    uint32_t page_size;
    uint32_t block_size;
    bool skip_bad_blocks;
    bool use_oob;
} nand_config_t;

// NAND接口实现
static jtag_error_t nand_build_write_cmd(const void* config, char* cmd, size_t size) {
    const nand_config_t* nand_cfg = (const nand_config_t*)config;
    // NAND特定的命令格式
    snprintf(cmd, size, "nand write %d \"%s\" 0x%08x", 
             nand_cfg->device_id, filename, offset);
    return JTAG_SUCCESS;
}
```

### 3. eMMC存储介质实现

```c
// eMMC特定配置
typedef struct emmc_config {
    uint32_t device_id;
    uint32_t partition;
    uint32_t block_size;
    bool enable_boot_partition;
} emmc_config_t;

// eMMC接口实现
static jtag_error_t emmc_build_write_cmd(const void* config, char* cmd, size_t size) {
    const emmc_config_t* emmc_cfg = (const emmc_config_t*)config;
    // eMMC特定的命令格式
    snprintf(cmd, size, "emmc write %d %d \"%s\" 0x%08x", 
             emmc_cfg->device_id, emmc_cfg->partition, filename, offset);
    return JTAG_SUCCESS;
}
```

### 4. FPGA bit文件处理器

```c
// BIT文件处理器
static file_format_handler_t bit_handler = {
    .format = FILE_FORMAT_BIT,
    .name = "Xilinx Bitstream",
    .extensions = (const char*[]){"bit", "bin", NULL},
    .requires_address = false,
    .has_embedded_address = false,
    .openocd_type_name = "bin",
    .detect = bit_file_detect,
    .validate = bit_file_validate,
    .get_info = bit_file_get_info
};

static jtag_error_t bit_file_detect(const char* filename) {
    // 检测BIT文件头部特征
    // Xilinx BIT文件通常以特定的magic number开始
    return JTAG_SUCCESS;
}
```

## 配置文件驱动的命令模板

### 命令模板配置文件 (JSON格式)

```json
{
  "command_templates": {
    "flash": {
      "write": {
        "bin": "flash write_image erase \"{filename}\" 0x{address} bin",
        "hex": "flash write_image erase \"{filename}\"",
        "elf": "flash write_image erase \"{filename}\""
      },
      "verify": {
        "bin": "verify_image \"{filename}\" 0x{address} bin",
        "hex": "verify_image \"{filename}\""
      }
    },
    "nand": {
      "write": {
        "bin": "nand write {device_id} \"{filename}\" 0x{offset}",
        "bit": "nand write {device_id} \"{filename}\" 0x{offset}"
      },
      "erase": {
        "all": "nand erase {device_id} 0x{offset} 0x{length}"
      }
    },
    "emmc": {
      "write": {
        "bin": "emmc write {device_id} {partition} \"{filename}\" 0x{offset}"
      }
    },
    "fpga": {
      "configure": {
        "bit": "fpga configure \"{filename}\"",
        "rbf": "fpga configure \"{filename}\" rbf"
      }
    }
  },
  "version_compatibility": {
    "0.11.0": {
      "supports": ["flash", "nand", "emmc"],
      "new_features": ["quad_en", "unlock"]
    },
    "0.12.0": {
      "supports": ["flash", "nand", "emmc", "fpga"],
      "new_features": ["fpga_configure"]
    }
  }
}
```

## 使用示例

### 1. 注册存储介质

```c
// 注册所有支持的存储介质
storage_manager_register(&flash_interface);
storage_manager_register(&nand_interface);
storage_manager_register(&emmc_interface);
storage_manager_register(&fpga_interface);
```

### 2. 注册文件格式处理器

```c
// 注册文件格式处理器
file_format_manager_register(&bin_handler);
file_format_manager_register(&hex_handler);
file_format_manager_register(&elf_handler);
file_format_manager_register(&bit_handler);
```

### 3. 统一的操作接口

```c
// 统一的写入操作
jtag_error_t universal_write(const char* filename, 
                           storage_type_t storage_type,
                           const void* storage_config) {
    
    // 1. 检测文件格式
    file_format_t format = file_format_detect(filename);
    
    // 2. 获取存储介质接口
    storage_interface_t* interface = storage_manager_get(storage_type);
    
    // 3. 构建命令
    char command[MAX_COMMAND_LENGTH];
    interface->build_write_cmd(storage_config, command, sizeof(command));
    
    // 4. 执行操作
    return interface->write(storage_config, &params);
}
```

## 优势分析

### 1. 可扩展性
- 新存储介质只需实现接口即可
- 新文件格式可独立添加
- 命令格式可通过配置文件定义

### 2. 维护性
- 每个模块职责单一
- 接口标准化
- 配置与代码分离

### 3. 兼容性
- 版本兼容性集中管理
- 向后兼容保证
- 渐进式升级支持

### 4. 测试性
- 每个模块可独立测试
- 接口模拟容易实现
- 集成测试清晰

这个架构设计能够很好地支持您未来添加NAND、eMMC、bit文件等功能的需求，同时保持代码的清晰和可维护性。
