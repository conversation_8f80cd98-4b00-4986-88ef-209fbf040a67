#ifndef OPENOCD_COMMAND_BUILDER_H
#define OPENOCD_COMMAND_BUILDER_H

#include "jtag_writer.h"
#include "openocd_client.h"
#include "flash_operations.h"

#ifdef __cplusplus
extern "C" {
#endif

// Command building options
typedef struct {
    bool erase;         // Add 'erase' option
    bool unlock;        // Add 'unlock' option  
    bool quad_en;       // Add 'quad_en' option (for SPI flash)
    bool verify;        // Add verification after write
} openocd_command_options_t;

// OpenOCD version information structure
typedef struct {
    int major;
    int minor;
    int patch;
    char version_string[64];
    bool supports_quad_en;
    bool supports_unlock;
} openocd_version_info_t;

/**
 * Detect OpenOCD version and capabilities
 * @param client OpenOCD client connection
 * @return JTAG_SUCCESS on success, error code on failure
 */
jtag_error_t openocd_command_detect_version(openocd_client_t* client);

/**
 * Build flash write_image command with proper format
 * @param config Flash configuration
 * @param options Command options (can be NULL for defaults)
 * @param command Output buffer for command
 * @param command_size Size of command buffer
 * @return JTAG_SUCCESS on success, error code on failure
 */
jtag_error_t openocd_command_build_write_image(const flash_config_t* config,
                                              const openocd_command_options_t* options,
                                              char* command,
                                              size_t command_size);

/**
 * Build verify_image command with proper format
 * @param config Flash configuration
 * @param command Output buffer for command
 * @param command_size Size of command buffer
 * @return JTAG_SUCCESS on success, error code on failure
 */
jtag_error_t openocd_command_build_verify_image(const flash_config_t* config,
                                               char* command,
                                               size_t command_size);

/**
 * Test command format and adapt if needed
 * @param client OpenOCD client connection
 * @param test_command Command to test
 * @param adapted_command Output buffer for adapted command
 * @param command_size Size of command buffer
 * @return JTAG_SUCCESS if command works, error code if adaptation needed
 */
jtag_error_t openocd_command_test_and_adapt(openocd_client_t* client,
                                           const char* test_command,
                                           char* adapted_command,
                                           size_t command_size);

/**
 * Get detected OpenOCD version information
 * @return Pointer to version info, or NULL if not detected
 */
const openocd_version_info_t* openocd_command_get_version_info(void);

/**
 * Reset version detection (for testing)
 */
void openocd_command_reset_version_detection(void);

// Helper macros for common command options
#define OPENOCD_CMD_OPTIONS_DEFAULT() { \
    .erase = true, \
    .unlock = false, \
    .quad_en = false, \
    .verify = false \
}

#define OPENOCD_CMD_OPTIONS_ERASE_ONLY() { \
    .erase = true, \
    .unlock = false, \
    .quad_en = false, \
    .verify = false \
}

#define OPENOCD_CMD_OPTIONS_NO_ERASE() { \
    .erase = false, \
    .unlock = false, \
    .quad_en = false, \
    .verify = false \
}

#define OPENOCD_CMD_OPTIONS_FULL() { \
    .erase = true, \
    .unlock = true, \
    .quad_en = false, \
    .verify = true \
}

#ifdef __cplusplus
}
#endif

#endif // OPENOCD_COMMAND_BUILDER_H
