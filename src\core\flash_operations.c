#include "flash_operations.h"
#include "logger.h"
#include <time.h>

/**
 * Get firmware file type
 */
firmware_type_t flash_detect_file_type(const char* filename) {
    if (!filename) {
        return FIRMWARE_TYPE_AUTO;
    }

    const char* ext = strrchr(filename, '.');
    if (!ext) {
        return FIRMWARE_TYPE_BIN; // Default to binary
    }

    ext++; // Skip the dot

    if (strcasecmp(ext, "bin") == 0) {
        return FIRMWARE_TYPE_BIN;
    } else if (strcasecmp(ext, "hex") == 0) {
        return FIRMWARE_TYPE_HEX;
    } else if (strcasecmp(ext, "elf") == 0) {
        return FIRMWARE_TYPE_ELF;
    } else if (strcasecmp(ext, "s19") == 0 || strcasecmp(ext, "srec") == 0) {
        return FIRMWARE_TYPE_S19;
    }

    return FIRMWARE_TYPE_BIN; // Default to binary
}

/**
 * Get firmware file size
 */
jtag_error_t flash_get_file_size(const char* filename, uint32_t* file_size) {
    if (!filename || !file_size) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    FILE* file = fopen(filename, "rb");
    if (!file) {
        LOG_ERROR("Cannot open file: %s", filename);
        return JTAG_ERROR_FILE_NOT_FOUND;
    }

    fseek(file, 0, SEEK_END);
    long size = ftell(file);
    fclose(file);

    if (size < 0) {
        LOG_ERROR("Failed to get file size: %s", filename);
        return JTAG_ERROR_FILE_NOT_FOUND;
    }

    *file_size = (uint32_t)size;
    return JTAG_SUCCESS;
}

/**
 * Get file type string
 */
static const char* get_file_type_string(firmware_type_t type) {
    switch (type) {
        case FIRMWARE_TYPE_BIN: return "bin";
        case FIRMWARE_TYPE_HEX: return "ihex";
        case FIRMWARE_TYPE_ELF: return "elf";
        case FIRMWARE_TYPE_S19: return "s19";
        default: return "bin";
    }
}

/**
 * Erase Flash sectors
 */
flash_result_t flash_erase_sectors(openocd_client_t* client,
                                  int bank_id,
                                  uint32_t start_sector,
                                  uint32_t end_sector,
                                  progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();

    if (!client) {
        strcpy(result.error_message, "Client parameter is null");
        return result;
    }

    LOG_INFO("Erasing Flash sectors %u-%u (bank %d)", start_sector, end_sector, bank_id);

    if (callback) {
        callback(0, "Starting Flash sector erase...");
    }

    // Construct erase command
    snprintf(command, sizeof(command), "flash erase_sector %d %u %u",
             bank_id, start_sector, end_sector);

    jtag_error_t ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to send erase command: %s", jtag_error_string(ret));
        return result;
    }

    // Check for errors in response
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Erase failed: %s", response);
        return result;
    }

    if (callback) {
        callback(100, "Flash sector erase completed");
    }

    result.success = true;
    result.bytes_processed = (end_sector - start_sector + 1) * 4096; // Assume 4KB sector size
    result.total_bytes = result.bytes_processed;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;

    LOG_INFO("Flash sector erase completed, elapsed time: %.2f seconds", result.elapsed_time);
    return result;
}

/**
 * Mass erase entire Flash
 */
flash_result_t flash_mass_erase(openocd_client_t* client,
                               int bank_id,
                               progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();

    if (!client) {
        strcpy(result.error_message, "Client parameter is null");
        return result;
    }

    LOG_INFO("Starting mass erase of Flash (bank %d)", bank_id);

    if (callback) {
        callback(0, "Starting Flash mass erase...");
    }

    // Initialize target first
    if (callback) {
        callback(5, "Initializing target...");
    }

    jtag_error_t ret = openocd_client_send_command(client, "init", response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to initialize target: %s", jtag_error_string(ret));
        return result;
    }

    // Reset and halt target
    if (callback) {
        callback(10, "Halting target...");
    }

    ret = openocd_client_send_command(client, "reset halt", response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to halt target: %s", jtag_error_string(ret));
        return result;
    }

    if (callback) {
        callback(20, "Erasing Flash...");
    }

    // Construct mass erase command
    snprintf(command, sizeof(command), "flash erase_sector %d 0 last", bank_id);

    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to send mass erase command: %s", jtag_error_string(ret));
        return result;
    }

    // Check for errors in response
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Mass erase failed: %s", response);
        return result;
    }

    if (callback) {
        callback(100, "Flash mass erase completed");
    }

    result.success = true;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;

    LOG_INFO("Flash mass erase completed, elapsed time: %.2f seconds", result.elapsed_time);
    return result;
}

/**
 * Write to Flash
 */
flash_result_t flash_write_file(openocd_client_t* client,
                               const flash_config_t* config,
                               progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();

    if (!client || !config) {
        strcpy(result.error_message, "Parameters are null");
        return result;
    }

    // Check if file exists
    uint32_t file_size;
    jtag_error_t ret = flash_get_file_size(config->firmware_file, &file_size);
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Cannot get file size: %s", config->firmware_file);
        return result;
    }

    result.total_bytes = file_size;

    LOG_INFO("Starting file programming: %s (size: %u bytes)", config->firmware_file, file_size);

    if (callback) {
        callback(0, "Starting firmware programming...");
    }

    // Erase Flash first if needed (includes target initialization)
    if (config->erase_before_write) {
        if (callback) {
            callback(5, "Erasing Flash...");
        }

        flash_result_t erase_result = flash_mass_erase(client, 0, callback);
        if (!erase_result.success) {
            strcpy(result.error_message, erase_result.error_message);
            return result;
        }
    } else {
        // Initialize target if not erasing
        if (callback) {
            callback(5, "Initializing target...");
        }

        ret = openocd_client_send_command(client, "init", response, sizeof(response));
        if (ret != JTAG_SUCCESS) {
            snprintf(result.error_message, sizeof(result.error_message),
                    "Failed to initialize target: %s", jtag_error_string(ret));
            return result;
        }

        // Reset and halt target
        if (callback) {
            callback(8, "Halting target...");
        }

        ret = openocd_client_send_command(client, "reset halt", response, sizeof(response));
        if (ret != JTAG_SUCCESS) {
            snprintf(result.error_message, sizeof(result.error_message),
                    "Failed to halt target: %s", jtag_error_string(ret));
            return result;
        }
    }

    if (callback) {
        callback(30, "Writing firmware...");
    }

    // Construct write command
    const char* file_type = get_file_type_string(config->file_type);
    if (config->file_type == FIRMWARE_TYPE_BIN) {
        // Binary files need address specification
        snprintf(command, sizeof(command), "flash write_image erase \"%s\" 0x%08x %s",
                 config->firmware_file, config->base_address, file_type);
    } else {
        // Other format files contain address information
        snprintf(command, sizeof(command), "flash write_image erase \"%s\" %s",
                 config->firmware_file, file_type);
    }

    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to send write command: %s", jtag_error_string(ret));
        return result;
    }

    // Check for errors in response
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Write failed: %s", response);
        return result;
    }

    if (callback) {
        callback(80, "Firmware write completed");
    }

    // Verify written content if needed
    if (config->verify_after_write) {
        if (callback) {
            callback(85, "Verifying firmware...");
        }

        flash_result_t verify_result = flash_verify_file(client, config, NULL);
        if (!verify_result.success) {
            snprintf(result.error_message, sizeof(result.error_message),
                    "Verification failed: %s", verify_result.error_message);
            return result;
        }
    }

    if (callback) {
        callback(100, "Firmware programming completed");
    }

    result.success = true;
    result.bytes_processed = file_size;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;

    LOG_INFO("Firmware programming completed, elapsed time: %.2f seconds", result.elapsed_time);
    return result;
}

/**
 * Verify Flash content
 */
flash_result_t flash_verify_file(openocd_client_t* client,
                                const flash_config_t* config,
                                progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();

    if (!client || !config) {
        strcpy(result.error_message, "Parameters are null");
        return result;
    }

    // Check if file exists
    uint32_t file_size;
    jtag_error_t ret = flash_get_file_size(config->firmware_file, &file_size);
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Cannot get file size: %s", config->firmware_file);
        return result;
    }

    result.total_bytes = file_size;

    LOG_INFO("Starting file verification: %s", config->firmware_file);

    if (callback) {
        callback(0, "Starting firmware verification...");
    }

    // Initialize target first
    if (callback) {
        callback(5, "Initializing target...");
    }

    ret = openocd_client_send_command(client, "init", response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to initialize target: %s", jtag_error_string(ret));
        return result;
    }

    // Reset and halt target
    if (callback) {
        callback(10, "Halting target...");
    }

    ret = openocd_client_send_command(client, "reset halt", response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to halt target: %s", jtag_error_string(ret));
        return result;
    }

    if (callback) {
        callback(20, "Verifying firmware...");
    }

    // Construct verify command
    const char* file_type = get_file_type_string(config->file_type);
    if (config->file_type == FIRMWARE_TYPE_BIN) {
        snprintf(command, sizeof(command), "verify_image \"%s\" 0x%08x %s",
                 config->firmware_file, config->base_address, file_type);
    } else {
        snprintf(command, sizeof(command), "verify_image \"%s\" %s",
                 config->firmware_file, file_type);
    }

    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to send verify command: %s", jtag_error_string(ret));
        return result;
    }

    // Check for errors in response
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Verification failed: %s", response);
        return result;
    }

    if (callback) {
        callback(100, "Firmware verification completed");
    }

    result.success = true;
    result.bytes_processed = file_size;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;

    LOG_INFO("Firmware verification completed, elapsed time: %.2f seconds", result.elapsed_time);
    return result;
}

/**
 * Read Flash content
 */
flash_result_t flash_read_to_file(openocd_client_t* client,
                                 int bank_id,
                                 uint32_t address,
                                 uint32_t length,
                                 const char* output_file,
                                 progress_callback_t callback) {
    flash_result_t result = {0};
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    clock_t start_time = clock();

    if (!client || !output_file) {
        strcpy(result.error_message, "Parameters are null");
        return result;
    }

    LOG_INFO("Starting Flash read: bank=%d, address=0x%08x, length=%u", bank_id, address, length);

    if (callback) {
        callback(0, "Starting Flash read...");
    }

    // Initialize target first
    if (callback) {
        callback(5, "Initializing target...");
    }

    jtag_error_t ret = openocd_client_send_command(client, "init", response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to initialize target: %s", jtag_error_string(ret));
        return result;
    }

    // Reset and halt target
    if (callback) {
        callback(10, "Halting target...");
    }

    ret = openocd_client_send_command(client, "reset halt", response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to halt target: %s", jtag_error_string(ret));
        return result;
    }

    if (callback) {
        callback(20, "Reading Flash...");
    }

    // If length is 0, read entire bank
    if (length == 0) {
        snprintf(command, sizeof(command), "flash read_bank %d \"%s\"", bank_id, output_file);
    } else {
        snprintf(command, sizeof(command), "flash read_bank %d \"%s\" 0x%08x %u",
                 bank_id, output_file, address, length);
    }

    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Failed to send read command: %s", jtag_error_string(ret));
        return result;
    }

    // Check for errors in response
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        snprintf(result.error_message, sizeof(result.error_message),
                "Read failed: %s", response);
        return result;
    }

    if (callback) {
        callback(100, "Flash read completed");
    }

    result.success = true;
    result.bytes_processed = length;
    result.total_bytes = length;
    result.elapsed_time = (double)(clock() - start_time) / CLOCKS_PER_SEC;

    LOG_INFO("Flash read completed, elapsed time: %.2f seconds", result.elapsed_time);
    return result;
}

/**
 * Get Flash bank information
 */
jtag_error_t flash_get_bank_info(openocd_client_t* client,
                                int bank_id,
                                flash_bank_info_t* info) {
    if (!client || !info) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    char response[MAX_RESPONSE_LENGTH];
    jtag_error_t ret = openocd_client_get_flash_banks(client, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        return ret;
    }

    // Parse Flash bank information
    // Need to parse OpenOCD returned Flash bank information
    // Format is usually: "#0 : <driver> at 0x<address>, size 0x<size>, buswidth <width>, chipwidth <width>"

    memset(info, 0, sizeof(flash_bank_info_t));
    info->bank_id = bank_id;

    // Simplified parsing, should be more comprehensive in practice
    char* line = strtok(response, "\n");
    while (line) {
        if (strstr(line, "#") && strstr(line, ":")) {
            int id;
            if (sscanf(line, "#%d", &id) == 1 && id == bank_id) {
                // Parse address
                char* addr_str = strstr(line, "at 0x");
                if (addr_str) {
                    sscanf(addr_str, "at 0x%x", &info->base_address);
                }

                // Parse size
                char* size_str = strstr(line, "size 0x");
                if (size_str) {
                    sscanf(size_str, "size 0x%x", &info->size);
                }

                // Parse driver name
                char* colon = strchr(line, ':');
                if (colon) {
                    colon += 2; // Skip ": "
                    char* space = strchr(colon, ' ');
                    if (space) {
                        int len = space - colon;
                        if (len < sizeof(info->driver_name) - 1) {
                            strncpy(info->driver_name, colon, len);
                            info->driver_name[len] = '\0';
                        }
                    }
                }

                break;
            }
        }
        line = strtok(NULL, "\n");
    }

    return JTAG_SUCCESS;
}

/**
 * List all Flash banks
 */
jtag_error_t flash_list_banks(openocd_client_t* client,
                             flash_bank_info_t* banks,
                             int max_banks,
                             int* num_banks) {
    if (!client || !banks || !num_banks) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    char response[MAX_RESPONSE_LENGTH];
    jtag_error_t ret = openocd_client_get_flash_banks(client, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        return ret;
    }

    *num_banks = 0;

    // Parse all Flash bank information
    char* line = strtok(response, "\n");
    while (line && *num_banks < max_banks) {
        if (strstr(line, "#") && strstr(line, ":")) {
            int id;
            if (sscanf(line, "#%d", &id) == 1) {
                flash_bank_info_t* info = &banks[*num_banks];
                memset(info, 0, sizeof(flash_bank_info_t));
                info->bank_id = id;

                // Parse detailed information (similar to above)
                char* addr_str = strstr(line, "at 0x");
                if (addr_str) {
                    sscanf(addr_str, "at 0x%x", &info->base_address);
                }

                char* size_str = strstr(line, "size 0x");
                if (size_str) {
                    sscanf(size_str, "size 0x%x", &info->size);
                }

                char* colon = strchr(line, ':');
                if (colon) {
                    colon += 2;
                    char* space = strchr(colon, ' ');
                    if (space) {
                        int len = space - colon;
                        if (len < sizeof(info->driver_name) - 1) {
                            strncpy(info->driver_name, colon, len);
                            info->driver_name[len] = '\0';
                        }
                    }
                }

                (*num_banks)++;
            }
        }
        line = strtok(NULL, "\n");
    }

    return JTAG_SUCCESS;
}
