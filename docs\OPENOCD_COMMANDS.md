# OpenOCD Commands Reference

This document provides a comprehensive reference for OpenOCD commands used by the JTAG Writer tool.

## Flash Commands

### flash write_image

Write firmware image to flash memory.

**Syntax:**
```
flash write_image [erase] [unlock] [quad_en] filename [offset [file_type]]
```

**Parameters:**
- `erase`: Erase flash before writing (optional)
- `unlock`: Unlock flash before writing (optional)
- `quad_en`: Enable quad mode for SPI flash (optional)
- `filename`: Path to firmware file (required)
- `offset`: Base address for binary files (required for .bin files)
- `file_type`: File format specification (optional, auto-detected if not specified)

**Supported File Types:**
- `bin`: Binary file (requires offset)
- `hex`: Intel HEX file
- `elf`: ELF executable file
- `s19`: Motorola S-record file

**Examples:**
```bash
# Write binary file with erase
flash write_image erase firmware.bin 0x08000000 bin

# Write HEX file with erase
flash write_image erase firmware.hex

# Write ELF file with erase
flash write_image erase firmware.elf

# Write without erase
flash write_image firmware.hex
```

### verify_image

Verify flash contents against firmware file.

**Syntax:**
```
verify_image filename [offset [file_type]]
```

**Examples:**
```bash
# Verify binary file
verify_image firmware.bin 0x08000000 bin

# Verify HEX file
verify_image firmware.hex
```

### flash erase_sector

Erase specific flash sectors.

**Syntax:**
```
flash erase_sector bank_id first_sector last_sector
```

**Examples:**
```bash
# Erase sectors 0-3 of bank 0
flash erase_sector 0 0 3
```

### flash erase_address

Erase flash by address range.

**Syntax:**
```
flash erase_address address length
```

**Examples:**
```bash
# Erase 64KB starting from 0x08000000
flash erase_address 0x08000000 0x10000
```

### flash read_bank

Read flash contents to file.

**Syntax:**
```
flash read_bank bank_id filename [offset [length]]
```

**Examples:**
```bash
# Read entire bank 0
flash read_bank 0 flash_dump.bin

# Read 4KB from offset 0x1000
flash read_bank 0 partial_dump.bin 0x1000 0x1000
```

## Target Commands

### init

Initialize all configured targets and interfaces.

**Syntax:**
```
init
```

### reset

Reset target processor.

**Syntax:**
```
reset [halt|run]
```

**Examples:**
```bash
# Reset and halt
reset halt

# Reset and run
reset run

# Reset (default behavior)
reset
```

### halt

Halt target processor.

**Syntax:**
```
halt
```

### resume

Resume target execution.

**Syntax:**
```
resume [address]
```

**Examples:**
```bash
# Resume from current PC
resume

# Resume from specific address
resume 0x08000000
```

## Information Commands

### version

Display OpenOCD version information.

**Syntax:**
```
version
```

### targets

Display available targets.

**Syntax:**
```
targets
```

### flash banks

Display configured flash banks.

**Syntax:**
```
flash banks
```

## Command Construction Guidelines

### Binary Files
For binary files, always specify the base address:
```c
snprintf(command, sizeof(command), "flash write_image erase \"%s\" 0x%08x bin",
         filename, base_address);
```

### Other File Formats
For HEX, ELF, and S19 files, address information is embedded:
```c
snprintf(command, sizeof(command), "flash write_image erase \"%s\"", filename);
```

### Error Handling
Always check command responses for error indicators:
- "error"
- "Error" 
- "failed"
- "Failed"

### Command Logging
All commands sent to OpenOCD are logged for debugging purposes:
```c
LOG_INFO("OpenOCD Command: %s", command);
```

## Version Compatibility

This command reference is based on OpenOCD 0.11.0 and later versions. Some commands may not be available in older versions.

### Known Variations
- Older versions may not support `quad_en` parameter
- Some flash drivers may have specific requirements
- Target-specific commands may vary

## Implementation Notes

### Current Implementation
The JTAG Writer currently uses these command patterns:

1. **Write Operations:**
   - Binary: `flash write_image erase "file" 0xADDRESS bin`
   - Others: `flash write_image erase "file"`

2. **Verify Operations:**
   - Binary: `verify_image "file" 0xADDRESS bin`
   - Others: `verify_image "file"`

3. **Read Operations:**
   - `flash read_bank BANK "file" [offset] [length]`

### Future Enhancements
Consider implementing:
- Dynamic command format detection
- OpenOCD version-specific command adaptation
- Enhanced error parsing and reporting
- Support for additional flash operations

## See Also
- OpenOCD User's Guide
- Target-specific configuration files
- Flash driver documentation
