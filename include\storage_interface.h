#ifndef STORAGE_INTERFACE_H
#define STORAGE_INTERFACE_H

#include "jtag_writer.h"

#ifdef __cplusplus
extern "C" {
#endif

// 存储介质类型
typedef enum {
    STORAGE_TYPE_FLASH_NOR,     // NOR Flash
    STORAGE_TYPE_FLASH_NAND,    // NAND Flash  
    STORAGE_TYPE_EMMC,          // eMMC
    STORAGE_TYPE_FPGA,          // FPGA配置
    STORAGE_TYPE_UNKNOWN
} storage_type_t;

// 文件格式类型
typedef enum {
    FILE_FORMAT_BIN,    // 二进制文件
    FILE_FORMAT_HEX,    // Intel HEX
    FILE_FORMAT_ELF,    // ELF可执行文件
    FILE_FORMAT_S19,    // Motorola S-record
    FILE_FORMAT_BIT,    // Xilinx位流文件
    FILE_FORMAT_RBF,    // Altera Raw Binary File
    FILE_FORMAT_AUTO    // 自动检测
} file_format_t;

// 操作类型
typedef enum {
    OPERATION_WRITE,
    OPERATION_READ,
    OPERATION_ERASE,
    OPERATION_VERIFY,
    OPERATION_CONFIGURE  // For FPGA
} operation_type_t;

// 通用操作参数
typedef struct {
    const char* filename;
    uint32_t address;
    uint32_t length;
    file_format_t format;
    bool auto_erase;
    bool auto_verify;
    void* storage_specific_config;
} operation_params_t;

// 文件信息结构
typedef struct {
    file_format_t format;
    uint32_t size;
    uint32_t entry_point;      // For ELF files
    uint32_t load_address;     // For files with embedded address
    bool has_embedded_address;
    char description[256];
} file_info_t;

// 存储介质特性
typedef struct {
    bool supports_erase;
    bool supports_verify;
    bool supports_partial_write;
    bool supports_bad_block_management;
    uint32_t min_write_size;
    uint32_t erase_block_size;
} storage_capabilities_t;

// 前向声明
struct storage_interface;
struct file_format_handler;

// 存储介质接口
typedef struct storage_interface {
    storage_type_t type;
    const char* name;
    const char* description;
    
    // 初始化和清理
    jtag_error_t (*init)(openocd_client_t* client, void* config);
    jtag_error_t (*cleanup)(void* context);
    
    // 基本操作
    jtag_error_t (*write)(openocd_client_t* client, const operation_params_t* params);
    jtag_error_t (*read)(openocd_client_t* client, const operation_params_t* params);
    jtag_error_t (*erase)(openocd_client_t* client, const operation_params_t* params);
    jtag_error_t (*verify)(openocd_client_t* client, const operation_params_t* params);
    
    // 命令构建
    jtag_error_t (*build_command)(operation_type_t op, const operation_params_t* params, 
                                 char* command, size_t command_size);
    
    // 信息查询
    jtag_error_t (*get_capabilities)(storage_capabilities_t* caps);
    jtag_error_t (*get_info)(openocd_client_t* client, char* info, size_t info_size);
    
    // 特定配置
    void* default_config;
    size_t config_size;
    
} storage_interface_t;

// 文件格式处理器
typedef struct file_format_handler {
    file_format_t format;
    const char* name;
    const char** extensions;  // 支持的文件扩展名，以NULL结尾
    
    // 文件检测和验证
    jtag_error_t (*detect)(const char* filename);
    jtag_error_t (*validate)(const char* filename);
    jtag_error_t (*get_info)(const char* filename, file_info_t* info);
    
    // 格式特性
    bool requires_address;      // 是否需要指定地址
    bool has_embedded_address;  // 是否包含地址信息
    
    // OpenOCD参数
    const char* openocd_type_name;
    
} file_format_handler_t;

// 存储管理器函数
jtag_error_t storage_manager_init(void);
jtag_error_t storage_manager_cleanup(void);
jtag_error_t storage_manager_register(const storage_interface_t* interface);
storage_interface_t* storage_manager_get(storage_type_t type);
storage_interface_t* storage_manager_get_by_name(const char* name);
jtag_error_t storage_manager_list(storage_interface_t** interfaces, size_t* count);

// 文件格式管理器函数
jtag_error_t file_format_manager_init(void);
jtag_error_t file_format_manager_cleanup(void);
jtag_error_t file_format_manager_register(const file_format_handler_t* handler);
file_format_handler_t* file_format_manager_get(file_format_t format);
file_format_handler_t* file_format_manager_get_by_extension(const char* filename);
file_format_t file_format_detect(const char* filename);
jtag_error_t file_format_get_info(const char* filename, file_info_t* info);

// 统一操作接口
jtag_error_t universal_write(openocd_client_t* client,
                           const char* filename,
                           storage_type_t storage_type,
                           const operation_params_t* params);

jtag_error_t universal_read(openocd_client_t* client,
                          const char* filename,
                          storage_type_t storage_type,
                          const operation_params_t* params);

jtag_error_t universal_erase(openocd_client_t* client,
                           storage_type_t storage_type,
                           const operation_params_t* params);

jtag_error_t universal_verify(openocd_client_t* client,
                            const char* filename,
                            storage_type_t storage_type,
                            const operation_params_t* params);

// 命令模板系统函数
jtag_error_t command_template_build(const char* operation,
                                   storage_type_t storage_type,
                                   const operation_params_t* params,
                                   char* command,
                                   size_t command_size);

bool command_template_is_supported(const char* operation,
                                  storage_type_t storage_type,
                                  file_format_t file_format,
                                  int openocd_major,
                                  int openocd_minor);

// 辅助函数
const char* storage_type_to_string(storage_type_t type);
storage_type_t storage_type_from_string(const char* str);
const char* file_format_to_string(file_format_t format);
file_format_t file_format_from_string(const char* str);

// 配置辅助宏
#define STORAGE_INTERFACE_INIT(type_val, name_val, desc_val) { \
    .type = type_val, \
    .name = name_val, \
    .description = desc_val, \
    .init = NULL, \
    .cleanup = NULL, \
    .write = NULL, \
    .read = NULL, \
    .erase = NULL, \
    .verify = NULL, \
    .build_command = NULL, \
    .get_capabilities = NULL, \
    .get_info = NULL, \
    .default_config = NULL, \
    .config_size = 0 \
}

#define FILE_FORMAT_HANDLER_INIT(format_val, name_val, exts) { \
    .format = format_val, \
    .name = name_val, \
    .extensions = exts, \
    .detect = NULL, \
    .validate = NULL, \
    .get_info = NULL, \
    .requires_address = false, \
    .has_embedded_address = false, \
    .openocd_type_name = NULL \
}

#ifdef __cplusplus
}
#endif

#endif // STORAGE_INTERFACE_H
