# 存储介质集成示例

本文档展示如何使用新的可扩展存储架构来支持不同的存储介质和文件格式。

## 快速开始

### 1. 初始化系统

```c
#include "storage_interface.h"

int main() {
    // 初始化存储管理器
    storage_manager_init();
    file_format_manager_init();
    
    // 注册存储接口
    storage_manager_register(get_flash_nor_interface());
    storage_manager_register(get_nand_interface());
    storage_manager_register(get_emmc_interface());
    storage_manager_register(get_fpga_interface());
    
    // 使用统一接口进行操作
    // ...
    
    // 清理
    storage_manager_cleanup();
    file_format_manager_cleanup();
    
    return 0;
}
```

### 2. 基本使用示例

```c
// 写入Flash NOR
jtag_error_t write_flash_example(openocd_client_t* client) {
    operation_params_t params = {
        .filename = "firmware.bin",
        .address = 0x08000000,
        .length = 0,  // 自动检测
        .format = FILE_FORMAT_BIN,
        .auto_erase = true,
        .auto_verify = true,
        .storage_specific_config = NULL
    };
    
    return universal_write(client, params.filename, STORAGE_TYPE_FLASH_NOR, &params);
}

// 写入NAND Flash
jtag_error_t write_nand_example(openocd_client_t* client) {
    operation_params_t params = {
        .filename = "bootloader.bin",
        .address = 0x00000000,
        .length = 0,
        .format = FILE_FORMAT_BIN,
        .auto_erase = false,  // NAND通常需要手动擦除
        .auto_verify = false,
        .storage_specific_config = NULL
    };
    
    return universal_write(client, params.filename, STORAGE_TYPE_FLASH_NAND, &params);
}

// 配置FPGA
jtag_error_t configure_fpga_example(openocd_client_t* client) {
    operation_params_t params = {
        .filename = "design.bit",
        .address = 0,  // FPGA配置不需要地址
        .length = 0,
        .format = FILE_FORMAT_BIT,
        .auto_erase = false,
        .auto_verify = false,
        .storage_specific_config = NULL
    };
    
    // 使用特殊的configure操作
    storage_interface_t* fpga_interface = storage_manager_get(STORAGE_TYPE_FPGA);
    if (fpga_interface && fpga_interface->write) {
        return fpga_interface->write(client, &params);
    }
    
    return JTAG_ERROR_NOT_IMPLEMENTED;
}
```

## 添加新的存储介质

### 1. 创建NAND Flash接口

```c
// src/storage/nand_storage.c

#include "storage_interface.h"

// NAND特定配置
typedef struct nand_config {
    uint32_t device_id;
    uint32_t page_size;
    uint32_t block_size;
    bool skip_bad_blocks;
    bool use_oob;
} nand_config_t;

static nand_config_t default_nand_config = {
    .device_id = 0,
    .page_size = 2048,
    .block_size = 128 * 1024,  // 128KB
    .skip_bad_blocks = true,
    .use_oob = false
};

static jtag_error_t nand_write(openocd_client_t* client, const operation_params_t* params) {
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    
    // 构建NAND写入命令
    jtag_error_t ret = command_template_build("write", STORAGE_TYPE_FLASH_NAND, 
                                             params, command, sizeof(command));
    if (ret != JTAG_SUCCESS) {
        return ret;
    }
    
    LOG_INFO("Writing to NAND: %s", params->filename);
    
    // 发送命令
    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("NAND write failed: %s", jtag_error_string(ret));
        return ret;
    }
    
    return JTAG_SUCCESS;
}

static storage_interface_t nand_interface = {
    .type = STORAGE_TYPE_FLASH_NAND,
    .name = "NAND Flash",
    .description = "NAND Flash memory storage interface",
    .write = nand_write,
    // ... 其他函数
    .default_config = &default_nand_config,
    .config_size = sizeof(nand_config_t)
};

storage_interface_t* get_nand_interface(void) {
    return &nand_interface;
}
```

### 2. 创建eMMC接口

```c
// src/storage/emmc_storage.c

typedef struct emmc_config {
    uint32_t device_id;
    uint32_t partition;
    uint32_t block_size;
    bool enable_boot_partition;
} emmc_config_t;

static jtag_error_t emmc_write(openocd_client_t* client, const operation_params_t* params) {
    // eMMC特定的写入逻辑
    char command[MAX_COMMAND_LENGTH];
    
    // 使用命令模板构建eMMC命令
    jtag_error_t ret = command_template_build("write", STORAGE_TYPE_EMMC, 
                                             params, command, sizeof(command));
    
    // 发送命令并处理响应
    // ...
    
    return JTAG_SUCCESS;
}

static storage_interface_t emmc_interface = {
    .type = STORAGE_TYPE_EMMC,
    .name = "eMMC",
    .description = "Embedded MultiMediaCard storage interface",
    .write = emmc_write,
    // ... 其他函数
};
```

## 添加新的文件格式

### 1. 创建ELF文件处理器

```c
// 在file_format_handlers.c中添加

static jtag_error_t elf_detect(const char* filename) {
    FILE* file = fopen(filename, "rb");
    if (!file) return JTAG_ERROR_FILE_NOT_FOUND;
    
    unsigned char elf_header[16];
    size_t read_bytes = fread(elf_header, 1, sizeof(elf_header), file);
    fclose(file);
    
    if (read_bytes >= 4) {
        // 检查ELF魔数: 0x7F 'E' 'L' 'F'
        if (elf_header[0] == 0x7F && 
            elf_header[1] == 'E' && 
            elf_header[2] == 'L' && 
            elf_header[3] == 'F') {
            return JTAG_SUCCESS;
        }
    }
    
    return JTAG_ERROR_FILE_FORMAT;
}

static jtag_error_t elf_get_info(const char* filename, file_info_t* info) {
    // 解析ELF文件头，获取入口点、加载地址等信息
    // ...
    
    info->format = FILE_FORMAT_ELF;
    info->has_embedded_address = true;
    // 设置其他字段
    
    return JTAG_SUCCESS;
}

static const char* elf_extensions[] = {"elf", NULL};

static file_format_handler_t elf_handler = {
    .format = FILE_FORMAT_ELF,
    .name = "ELF Executable",
    .extensions = elf_extensions,
    .detect = elf_detect,
    .get_info = elf_get_info,
    .requires_address = false,
    .has_embedded_address = true,
    .openocd_type_name = "elf"
};
```

### 2. 创建RBF文件处理器 (Altera)

```c
static jtag_error_t rbf_detect(const char* filename) {
    const char* ext = strrchr(filename, '.');
    if (ext && strcasecmp(ext, ".rbf") == 0) {
        return JTAG_SUCCESS;
    }
    return JTAG_ERROR_FILE_FORMAT;
}

static file_format_handler_t rbf_handler = {
    .format = FILE_FORMAT_RBF,
    .name = "Altera Raw Binary File",
    .extensions = (const char*[]){"rbf", NULL},
    .detect = rbf_detect,
    .requires_address = false,
    .has_embedded_address = false,
    .openocd_type_name = "rbf"
};
```

## 高级用法

### 1. 自动检测和处理

```c
jtag_error_t smart_write(openocd_client_t* client, const char* filename, uint32_t address) {
    // 自动检测文件格式
    file_format_t format = file_format_detect(filename);
    if (format == FILE_FORMAT_AUTO) {
        LOG_ERROR("Cannot detect file format for: %s", filename);
        return JTAG_ERROR_FILE_FORMAT;
    }
    
    // 获取文件信息
    file_info_t file_info;
    jtag_error_t ret = file_format_get_info(filename, &file_info);
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Cannot get file information");
        return ret;
    }
    
    // 根据文件大小选择存储介质
    storage_type_t storage_type;
    if (file_info.size > 16 * 1024 * 1024) {  // > 16MB
        storage_type = STORAGE_TYPE_EMMC;
    } else if (file_info.size > 1024 * 1024) {  // > 1MB
        storage_type = STORAGE_TYPE_FLASH_NAND;
    } else {
        storage_type = STORAGE_TYPE_FLASH_NOR;
    }
    
    // 准备操作参数
    operation_params_t params = {
        .filename = filename,
        .address = file_info.has_embedded_address ? file_info.load_address : address,
        .length = file_info.size,
        .format = format,
        .auto_erase = true,
        .auto_verify = true,
        .storage_specific_config = NULL
    };
    
    // 执行写入
    return universal_write(client, filename, storage_type, &params);
}
```

### 2. 批量操作

```c
jtag_error_t batch_write(openocd_client_t* client, const char** filenames, size_t count) {
    for (size_t i = 0; i < count; i++) {
        LOG_INFO("Processing file %zu/%zu: %s", i + 1, count, filenames[i]);
        
        jtag_error_t ret = smart_write(client, filenames[i], 0);
        if (ret != JTAG_SUCCESS) {
            LOG_ERROR("Failed to write file: %s", filenames[i]);
            return ret;
        }
    }
    
    LOG_INFO("Batch write completed successfully");
    return JTAG_SUCCESS;
}
```

## 配置和扩展

### 1. 运行时配置

```c
// 配置Flash NOR参数
flash_nor_set_config(0x08000000, 0x200000, 0);  // 2MB Flash at bank 0

// 配置NAND参数
nand_set_config(0, 2048, 128*1024, true);  // device 0, 2KB page, 128KB block

// 配置eMMC参数
emmc_set_config(0, 1, 512, true);  // device 0, partition 1, 512B block
```

### 2. 添加自定义存储介质

```c
// 创建自定义存储接口
static storage_interface_t custom_storage = {
    .type = STORAGE_TYPE_UNKNOWN,  // 或添加新的类型
    .name = "Custom Storage",
    .write = custom_write_function,
    .read = custom_read_function,
    // ...
};

// 注册自定义接口
storage_manager_register(&custom_storage);
```

这个架构的优势在于：

1. **模块化设计** - 每个存储介质和文件格式都是独立的模块
2. **易于扩展** - 添加新功能只需实现相应接口
3. **统一接口** - 应用层使用统一的API，无需关心底层实现
4. **配置驱动** - 命令格式通过模板系统管理，易于维护
5. **向后兼容** - 现有代码可以逐步迁移到新架构
