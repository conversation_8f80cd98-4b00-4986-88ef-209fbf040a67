#include "storage_interface.h"
#include "openocd_client.h"
#include "logger.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

// Flash NOR特定配置
typedef struct flash_nor_config {
    uint32_t base_address;
    uint32_t size;
    uint32_t sector_size;
    uint32_t bank_id;
    bool auto_erase;
    bool auto_verify;
    bool unlock_required;
} flash_nor_config_t;

// 默认配置
static flash_nor_config_t default_flash_config = {
    .base_address = 0x08000000,
    .size = 0x100000,           // 1MB
    .sector_size = 0x1000,      // 4KB
    .bank_id = 0,
    .auto_erase = true,
    .auto_verify = true,
    .unlock_required = false
};

// ============================================================================
// Flash NOR操作实现
// ============================================================================

static jtag_error_t flash_nor_init(openocd_client_t* client, void* config) {
    if (!client) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char response[MAX_RESPONSE_LENGTH];
    
    // 初始化目标
    jtag_error_t ret = openocd_client_send_command(client, "init", response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Failed to initialize target: %s", jtag_error_string(ret));
        return ret;
    }
    
    // 复位并停止目标
    ret = openocd_client_send_command(client, "reset halt", response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Failed to halt target: %s", jtag_error_string(ret));
        return ret;
    }
    
    LOG_INFO("Flash NOR storage initialized successfully");
    return JTAG_SUCCESS;
}

static jtag_error_t flash_nor_write(openocd_client_t* client, const operation_params_t* params) {
    if (!client || !params || !params->filename) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    
    // 构建写入命令
    jtag_error_t ret = command_template_build("write", STORAGE_TYPE_FLASH_NOR, 
                                             params, command, sizeof(command));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Failed to build write command");
        return ret;
    }
    
    LOG_INFO("Writing to Flash NOR: %s", params->filename);
    LOG_DEBUG("Command: %s", command);
    
    // 发送命令
    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Flash write failed: %s", jtag_error_string(ret));
        return ret;
    }
    
    // 检查响应中的错误
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        LOG_ERROR("Flash write failed: %s", response);
        return JTAG_ERROR_OPERATION_FAILED;
    }
    
    LOG_INFO("Flash write completed successfully");
    return JTAG_SUCCESS;
}

static jtag_error_t flash_nor_read(openocd_client_t* client, const operation_params_t* params) {
    if (!client || !params || !params->filename) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    
    // 构建读取命令
    jtag_error_t ret = command_template_build("read", STORAGE_TYPE_FLASH_NOR, 
                                             params, command, sizeof(command));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Failed to build read command");
        return ret;
    }
    
    LOG_INFO("Reading from Flash NOR to: %s", params->filename);
    LOG_DEBUG("Command: %s", command);
    
    // 发送命令
    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Flash read failed: %s", jtag_error_string(ret));
        return ret;
    }
    
    // 检查响应中的错误
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        LOG_ERROR("Flash read failed: %s", response);
        return JTAG_ERROR_OPERATION_FAILED;
    }
    
    LOG_INFO("Flash read completed successfully");
    return JTAG_SUCCESS;
}

static jtag_error_t flash_nor_erase(openocd_client_t* client, const operation_params_t* params) {
    if (!client || !params) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    
    // 构建擦除命令
    jtag_error_t ret = command_template_build("erase", STORAGE_TYPE_FLASH_NOR, 
                                             params, command, sizeof(command));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Failed to build erase command");
        return ret;
    }
    
    LOG_INFO("Erasing Flash NOR: address=0x%08x, length=0x%08x", params->address, params->length);
    LOG_DEBUG("Command: %s", command);
    
    // 发送命令
    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Flash erase failed: %s", jtag_error_string(ret));
        return ret;
    }
    
    // 检查响应中的错误
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        LOG_ERROR("Flash erase failed: %s", response);
        return JTAG_ERROR_OPERATION_FAILED;
    }
    
    LOG_INFO("Flash erase completed successfully");
    return JTAG_SUCCESS;
}

static jtag_error_t flash_nor_verify(openocd_client_t* client, const operation_params_t* params) {
    if (!client || !params || !params->filename) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char command[MAX_COMMAND_LENGTH];
    char response[MAX_RESPONSE_LENGTH];
    
    // 构建验证命令
    jtag_error_t ret = command_template_build("verify", STORAGE_TYPE_FLASH_NOR, 
                                             params, command, sizeof(command));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Failed to build verify command");
        return ret;
    }
    
    LOG_INFO("Verifying Flash NOR: %s", params->filename);
    LOG_DEBUG("Command: %s", command);
    
    // 发送命令
    ret = openocd_client_send_command(client, command, response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        LOG_ERROR("Flash verify failed: %s", jtag_error_string(ret));
        return ret;
    }
    
    // 检查响应中的错误
    if (strstr(response, "error") || strstr(response, "Error") || strstr(response, "failed")) {
        LOG_ERROR("Flash verify failed: %s", response);
        return JTAG_ERROR_OPERATION_FAILED;
    }
    
    LOG_INFO("Flash verify completed successfully");
    return JTAG_SUCCESS;
}

static jtag_error_t flash_nor_build_command(operation_type_t op, 
                                           const operation_params_t* params,
                                           char* command, 
                                           size_t command_size) {
    const char* operation_str = NULL;
    
    switch (op) {
        case OPERATION_WRITE:
            operation_str = "write";
            break;
        case OPERATION_READ:
            operation_str = "read";
            break;
        case OPERATION_ERASE:
            operation_str = "erase";
            break;
        case OPERATION_VERIFY:
            operation_str = "verify";
            break;
        default:
            return JTAG_ERROR_INVALID_ARGS;
    }
    
    return command_template_build(operation_str, STORAGE_TYPE_FLASH_NOR, 
                                 params, command, command_size);
}

static jtag_error_t flash_nor_get_capabilities(storage_capabilities_t* caps) {
    if (!caps) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    caps->supports_erase = true;
    caps->supports_verify = true;
    caps->supports_partial_write = true;
    caps->supports_bad_block_management = false;
    caps->min_write_size = 1;
    caps->erase_block_size = 4096;  // 4KB sectors
    
    return JTAG_SUCCESS;
}

static jtag_error_t flash_nor_get_info(openocd_client_t* client, char* info, size_t info_size) {
    if (!client || !info) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    char response[MAX_RESPONSE_LENGTH];
    
    // 获取Flash bank信息
    jtag_error_t ret = openocd_client_send_command(client, "flash banks", response, sizeof(response));
    if (ret != JTAG_SUCCESS) {
        snprintf(info, info_size, "Failed to get flash bank information");
        return ret;
    }
    
    strncpy(info, response, info_size - 1);
    info[info_size - 1] = '\0';
    
    return JTAG_SUCCESS;
}

static jtag_error_t flash_nor_cleanup(void* context) {
    // Flash NOR通常不需要特殊的清理操作
    LOG_DEBUG("Flash NOR storage cleanup completed");
    return JTAG_SUCCESS;
}

// ============================================================================
// Flash NOR存储接口定义
// ============================================================================

static storage_interface_t flash_nor_interface = {
    .type = STORAGE_TYPE_FLASH_NOR,
    .name = "Flash NOR",
    .description = "NOR Flash memory storage interface",
    
    .init = flash_nor_init,
    .cleanup = flash_nor_cleanup,
    .write = flash_nor_write,
    .read = flash_nor_read,
    .erase = flash_nor_erase,
    .verify = flash_nor_verify,
    .build_command = flash_nor_build_command,
    .get_capabilities = flash_nor_get_capabilities,
    .get_info = flash_nor_get_info,
    
    .default_config = &default_flash_config,
    .config_size = sizeof(flash_nor_config_t)
};

// ============================================================================
// 公共接口
// ============================================================================

storage_interface_t* get_flash_nor_interface(void) {
    return &flash_nor_interface;
}

jtag_error_t flash_nor_set_config(uint32_t base_address, uint32_t size, uint32_t bank_id) {
    default_flash_config.base_address = base_address;
    default_flash_config.size = size;
    default_flash_config.bank_id = bank_id;
    
    LOG_INFO("Flash NOR config updated: base=0x%08x, size=0x%08x, bank=%d",
             base_address, size, bank_id);
    
    return JTAG_SUCCESS;
}
