# OpenOCD Command Builder Integration Guide

This document explains how to integrate the new OpenOCD command builder into the existing JTAG Writer codebase.

## Overview

The OpenOCD command builder provides:
1. **Static command format documentation** - Comprehensive reference in `OPENOCD_COMMANDS.md`
2. **Dynamic command adaptation** - Runtime detection and format adjustment
3. **Version compatibility** - Support for different OpenOCD versions

## Integration Steps

### 1. Update CMakeLists.txt

Add the new source file to your build system:

```cmake
# Add to existing source files
set(SOURCES
    src/main.c
    src/core/flash_operations.c
    src/core/openocd_command_builder.c  # Add this line
    src/network/openocd_client.c
    # ... other sources
)
```

### 2. Update flash_operations.c

Replace the existing command construction with the new builder:

```c
#include "openocd_command_builder.h"

// In flash_write_file function, replace lines 280-290:
// OLD CODE:
/*
const char* file_type = get_file_type_string(config->file_type);
if (config->file_type == FIRMWARE_TYPE_BIN) {
    snprintf(command, sizeof(command), "flash write_image erase \"%s\" 0x%08x %s",
             config->firmware_file, config->base_address, file_type);
} else {
    snprintf(command, sizeof(command), "flash write_image erase \"%s\" %s",
             config->firmware_file, file_type);
}
*/

// NEW CODE:
openocd_command_options_t cmd_options = OPENOCD_CMD_OPTIONS_DEFAULT();
cmd_options.erase = config->erase_before_write;

ret = openocd_command_build_write_image(config, &cmd_options, command, sizeof(command));
if (ret != JTAG_SUCCESS) {
    snprintf(result.error_message, sizeof(result.error_message),
            "Failed to build write command: %s", jtag_error_string(ret));
    return result;
}
```

### 3. Update verify operations

Similarly, update verify command construction:

```c
// In flash_verify_file function, replace verify command construction:
// OLD CODE:
/*
const char* file_type = get_file_type_string(config->file_type);
if (config->file_type == FIRMWARE_TYPE_BIN) {
    snprintf(command, sizeof(command), "verify_image \"%s\" 0x%08x %s",
             config->firmware_file, config->base_address, file_type);
} else {
    snprintf(command, sizeof(command), "verify_image \"%s\" %s",
             config->firmware_file, file_type);
}
*/

// NEW CODE:
ret = openocd_command_build_verify_image(config, command, sizeof(command));
if (ret != JTAG_SUCCESS) {
    snprintf(result.error_message, sizeof(result.error_message),
            "Failed to build verify command: %s", jtag_error_string(ret));
    return result;
}
```

### 4. Initialize version detection

Add version detection during client connection:

```c
// In openocd_client_connect or similar initialization function:
jtag_error_t openocd_client_connect_enhanced(openocd_client_t* client) {
    // Existing connection code...
    jtag_error_t ret = openocd_client_connect(client);
    if (ret == JTAG_SUCCESS) {
        // Detect OpenOCD version and capabilities
        openocd_command_detect_version(client);
    }
    return ret;
}
```

## Usage Examples

### Basic Usage

```c
#include "openocd_command_builder.h"

// Create command options
openocd_command_options_t options = OPENOCD_CMD_OPTIONS_DEFAULT();

// Build write command
char command[MAX_COMMAND_LENGTH];
jtag_error_t ret = openocd_command_build_write_image(config, &options, 
                                                    command, sizeof(command));
if (ret == JTAG_SUCCESS) {
    // Send command to OpenOCD
    ret = openocd_client_send_command(client, command, response, sizeof(response));
}
```

### Advanced Usage with Options

```c
// Custom command options
openocd_command_options_t options = {
    .erase = true,      // Erase before write
    .unlock = true,     // Unlock flash if supported
    .quad_en = false,   // Don't enable quad mode
    .verify = false     // Don't add verify to command
};

// Build command with custom options
ret = openocd_command_build_write_image(config, &options, command, sizeof(command));
```

### Version-Aware Programming

```c
// Check OpenOCD capabilities
const openocd_version_info_t* version_info = openocd_command_get_version_info();
if (version_info) {
    LOG_INFO("OpenOCD version: %s", version_info->version_string);
    
    if (version_info->supports_quad_en) {
        // Enable quad mode for SPI flash
        options.quad_en = true;
    }
    
    if (version_info->supports_unlock) {
        // Use unlock feature
        options.unlock = true;
    }
}
```

### Command Testing and Adaptation

```c
// Test command format before actual operation
char test_command[MAX_COMMAND_LENGTH];
char adapted_command[MAX_COMMAND_LENGTH];

// Build initial command
openocd_command_build_write_image(config, &options, test_command, sizeof(test_command));

// Test and adapt if needed
ret = openocd_command_test_and_adapt(client, test_command, adapted_command, sizeof(adapted_command));
if (ret == JTAG_SUCCESS) {
    // Use adapted command
    ret = openocd_client_send_command(client, adapted_command, response, sizeof(response));
} else if (ret == JTAG_ERROR_COMMAND_SYNTAX) {
    LOG_WARN("Command format not supported, trying fallback");
    // Implement fallback logic
}
```

## Benefits

### 1. Maintainability
- Centralized command format logic
- Easy to update for new OpenOCD versions
- Clear separation of concerns

### 2. Compatibility
- Automatic version detection
- Graceful fallback for unsupported features
- Future-proof design

### 3. Debugging
- Enhanced logging of command construction
- Version information in logs
- Command format validation

### 4. Flexibility
- Easy to add new command options
- Configurable command behavior
- Support for different flash types

## Migration Strategy

### Phase 1: Documentation
- ✅ Create `OPENOCD_COMMANDS.md` reference
- ✅ Create command builder implementation
- ✅ Create integration guide

### Phase 2: Integration
- [ ] Update build system
- [ ] Integrate command builder in flash operations
- [ ] Add version detection to client connection
- [ ] Update existing command construction

### Phase 3: Enhancement
- [ ] Add command format testing
- [ ] Implement fallback mechanisms
- [ ] Add support for additional OpenOCD features
- [ ] Create unit tests for command builder

### Phase 4: Optimization
- [ ] Performance optimization
- [ ] Memory usage optimization
- [ ] Error handling improvements
- [ ] Documentation updates

## Testing

### Unit Tests
Create tests for:
- Command format generation
- Version detection
- Option handling
- Error conditions

### Integration Tests
Test with:
- Different OpenOCD versions
- Various target configurations
- Different file formats
- Error scenarios

## Future Enhancements

1. **Command Caching**: Cache successful command formats
2. **Auto-Discovery**: Automatically discover supported features
3. **Configuration**: Allow command format customization
4. **Metrics**: Collect usage statistics for optimization

## See Also
- `docs/OPENOCD_COMMANDS.md` - Command format reference
- `docs/USAGE.md` - User documentation
- OpenOCD official documentation
