#include "openocd_client.h"
#include "logger.h"

/**
 * Create OpenOCD client
 */
openocd_client_t* openocd_client_create(const openocd_config_t* config) {
    if (!config) {
        LOG_ERROR("Configuration parameter is null");
        return NULL;
    }

    openocd_client_t* client = malloc(sizeof(openocd_client_t));
    if (!client) {
        LOG_ERROR("Memory allocation failed");
        return NULL;
    }

    memset(client, 0, sizeof(openocd_client_t));
    client->socket = INVALID_SOCKET;
    client->config = *config;
    client->connected = false;

    LOG_DEBUG("OpenOCD client created successfully");
    return client;
}

/**
 * Destroy OpenOCD client
 */
void openocd_client_destroy(openocd_client_t* client) {
    if (!client) {
        return;
    }

    if (client->connected) {
        openocd_client_disconnect(client);
    }

    free(client);
    LOG_DEBUG("OpenOCD client destroyed");
}

/**
 * Connect to OpenOCD server
 */
jtag_error_t openocd_client_connect(openocd_client_t* client) {
    if (!client) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    if (client->connected) {
        LOG_WARN("Client already connected");
        return JTAG_SUCCESS;
    }

    // Create socket
    client->socket = socket(AF_INET, SOCK_STREAM, 0);
    if (client->socket == INVALID_SOCKET) {
        LOG_ERROR("Failed to create socket: %d", SOCKET_ERROR_CODE);
        return JTAG_ERROR_NETWORK_ERROR;
    }

    // Set socket options
    int opt = 1;
#ifdef _WIN32
    setsockopt(client->socket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));
#else
    setsockopt(client->socket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
#endif

    // Set timeout
    if (client->config.timeout_ms > 0) {
#ifdef _WIN32
        DWORD timeout = client->config.timeout_ms;
        setsockopt(client->socket, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
        setsockopt(client->socket, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));
#else
        struct timeval timeout;
        timeout.tv_sec = client->config.timeout_ms / 1000;
        timeout.tv_usec = (client->config.timeout_ms % 1000) * 1000;
        setsockopt(client->socket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
        setsockopt(client->socket, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
#endif
    }

    // Connect to server
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(client->config.tcl_port);

    if (inet_pton(AF_INET, client->config.host, &server_addr.sin_addr) <= 0) {
        LOG_ERROR("Invalid IP address: %s", client->config.host);
        CLOSE_SOCKET(client->socket);
        client->socket = INVALID_SOCKET;
        return JTAG_ERROR_NETWORK_ERROR;
    }

    if (connect(client->socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        LOG_ERROR("Failed to connect to server: %s:%d, error code: %d",
                 client->config.host, client->config.tcl_port, SOCKET_ERROR_CODE);
        CLOSE_SOCKET(client->socket);
        client->socket = INVALID_SOCKET;
        return JTAG_ERROR_NETWORK_ERROR;
    }

    client->connected = true;
    LOG_INFO("Successfully connected to OpenOCD server: %s:%d", client->config.host, client->config.tcl_port);

    return JTAG_SUCCESS;
}

/**
 * Disconnect from OpenOCD server
 */
void openocd_client_disconnect(openocd_client_t* client) {
    if (!client || !client->connected) {
        return;
    }

    if (client->socket != INVALID_SOCKET) {
        CLOSE_SOCKET(client->socket);
        client->socket = INVALID_SOCKET;
    }

    client->connected = false;
    LOG_INFO("Disconnected from OpenOCD server");
}

/**
 * Send command to OpenOCD
 */
jtag_error_t openocd_client_send_command(openocd_client_t* client,
                                        const char* command,
                                        char* response,
                                        size_t response_size) {
    if (!client || !command || !response) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    if (!client->connected) {
        LOG_ERROR("Client not connected");
        return JTAG_ERROR_NETWORK_ERROR;
    }

    // Construct full command (add terminator)
    char full_command[MAX_COMMAND_LENGTH];
    int cmd_len = snprintf(full_command, sizeof(full_command), "%s%s", command, OPENOCD_COMMAND_TOKEN);
    if (cmd_len >= sizeof(full_command)) {
        LOG_ERROR("Command too long");
        return JTAG_ERROR_INVALID_ARGS;
    }

    LOG_DEBUG("Sending command: %s", command);
    LOG_INFO("OpenOCD Command: %s", command);

    // Send command
    int sent = send(client->socket, full_command, cmd_len, 0);
    if (sent == SOCKET_ERROR || sent != cmd_len) {
        LOG_ERROR("Failed to send command: %d", SOCKET_ERROR_CODE);
        return JTAG_ERROR_NETWORK_ERROR;
    }

    // Receive response
    memset(response, 0, response_size);
    int total_received = 0;

    while (total_received < response_size - 1) {
        int received = recv(client->socket, response + total_received,
                          response_size - total_received - 1, 0);

        if (received == SOCKET_ERROR) {
            LOG_ERROR("Failed to receive response: %d", SOCKET_ERROR_CODE);
            return JTAG_ERROR_NETWORK_ERROR;
        }

        if (received == 0) {
            LOG_ERROR("Connection closed by server");
            client->connected = false;
            return JTAG_ERROR_NETWORK_ERROR;
        }

        total_received += received;

        // Check if terminator received
        if (total_received > 0 && response[total_received - 1] == '\x1a') {
            response[total_received - 1] = '\0'; // Remove terminator
            break;
        }
    }

    LOG_DEBUG("Received response: %s", response);
    LOG_INFO("OpenOCD Response: %s", response);
    return JTAG_SUCCESS;
}

/**
 * Check OpenOCD connection status
 */
bool openocd_client_is_connected(const openocd_client_t* client) {
    return client && client->connected;
}

/**
 * Get OpenOCD version information
 */
jtag_error_t openocd_client_get_version(openocd_client_t* client,
                                       char* version,
                                       size_t version_size) {
    return openocd_client_send_command(client, "version", version, version_size);
}

/**
 * Get target information
 */
jtag_error_t openocd_client_get_target_info(openocd_client_t* client,
                                           char* target_info,
                                           size_t info_size) {
    return openocd_client_send_command(client, "targets", target_info, info_size);
}

/**
 * Get Flash bank information
 */
jtag_error_t openocd_client_get_flash_banks(openocd_client_t* client,
                                           char* bank_info,
                                           size_t info_size) {
    return openocd_client_send_command(client, "flash banks", bank_info, info_size);
}

/**
 * Initialize target
 */
jtag_error_t openocd_client_init_target(openocd_client_t* client) {
    char response[MAX_RESPONSE_LENGTH];
    return openocd_client_send_command(client, "init", response, sizeof(response));
}

/**
 * Reset target
 */
jtag_error_t openocd_client_reset_target(openocd_client_t* client, bool halt) {
    char response[MAX_RESPONSE_LENGTH];
    const char* command = halt ? "reset halt" : "reset";
    return openocd_client_send_command(client, command, response, sizeof(response));
}

/**
 * Halt target
 */
jtag_error_t openocd_client_halt_target(openocd_client_t* client) {
    char response[MAX_RESPONSE_LENGTH];
    return openocd_client_send_command(client, "halt", response, sizeof(response));
}

/**
 * Resume target execution
 */
jtag_error_t openocd_client_resume_target(openocd_client_t* client) {
    char response[MAX_RESPONSE_LENGTH];
    return openocd_client_send_command(client, "resume", response, sizeof(response));
}
