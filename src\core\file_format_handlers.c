#include "storage_interface.h"
#include "logger.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <ctype.h>

// 文件格式处理器实现

// ============================================================================
// BIN文件处理器
// ============================================================================

static jtag_error_t bin_detect(const char* filename) {
    if (!filename) return JTAG_ERROR_INVALID_ARGS;
    
    const char* ext = strrchr(filename, '.');
    if (ext && (strcasecmp(ext, ".bin") == 0)) {
        return JTAG_SUCCESS;
    }
    return JTAG_ERROR_FILE_FORMAT;
}

static jtag_error_t bin_validate(const char* filename) {
    FILE* file = fopen(filename, "rb");
    if (!file) {
        return JTAG_ERROR_FILE_NOT_FOUND;
    }
    
    // 检查文件大小
    fseek(file, 0, SEEK_END);
    long size = ftell(file);
    fclose(file);
    
    if (size <= 0) {
        return JTAG_ERROR_FILE_FORMAT;
    }
    
    return JTAG_SUCCESS;
}

static jtag_error_t bin_get_info(const char* filename, file_info_t* info) {
    if (!filename || !info) return JTAG_ERROR_INVALID_ARGS;
    
    FILE* file = fopen(filename, "rb");
    if (!file) {
        return JTAG_ERROR_FILE_NOT_FOUND;
    }
    
    fseek(file, 0, SEEK_END);
    info->size = ftell(file);
    fclose(file);
    
    info->format = FILE_FORMAT_BIN;
    info->has_embedded_address = false;
    info->entry_point = 0;
    info->load_address = 0;
    snprintf(info->description, sizeof(info->description), 
             "Binary file, %u bytes", info->size);
    
    return JTAG_SUCCESS;
}

static const char* bin_extensions[] = {"bin", NULL};

static file_format_handler_t bin_handler = {
    .format = FILE_FORMAT_BIN,
    .name = "Binary",
    .extensions = bin_extensions,
    .detect = bin_detect,
    .validate = bin_validate,
    .get_info = bin_get_info,
    .requires_address = true,
    .has_embedded_address = false,
    .openocd_type_name = "bin"
};

// ============================================================================
// HEX文件处理器
// ============================================================================

static jtag_error_t hex_detect(const char* filename) {
    if (!filename) return JTAG_ERROR_INVALID_ARGS;
    
    const char* ext = strrchr(filename, '.');
    if (ext && (strcasecmp(ext, ".hex") == 0)) {
        return JTAG_SUCCESS;
    }
    
    // 检查文件内容
    FILE* file = fopen(filename, "r");
    if (!file) return JTAG_ERROR_FILE_NOT_FOUND;
    
    char line[256];
    bool is_hex = false;
    
    if (fgets(line, sizeof(line), file)) {
        // Intel HEX文件以':'开始
        if (line[0] == ':') {
            is_hex = true;
        }
    }
    
    fclose(file);
    return is_hex ? JTAG_SUCCESS : JTAG_ERROR_FILE_FORMAT;
}

static jtag_error_t hex_validate(const char* filename) {
    FILE* file = fopen(filename, "r");
    if (!file) {
        return JTAG_ERROR_FILE_NOT_FOUND;
    }
    
    char line[256];
    int line_num = 0;
    bool valid = true;
    
    while (fgets(line, sizeof(line), file) && valid) {
        line_num++;
        
        // 跳过空行
        if (strlen(line) <= 1) continue;
        
        // 检查HEX格式
        if (line[0] != ':') {
            LOG_ERROR("Invalid HEX format at line %d", line_num);
            valid = false;
            break;
        }
        
        // 检查长度和字符
        size_t len = strlen(line) - 1; // 减去换行符
        if (len < 11) { // 最小HEX记录长度
            LOG_ERROR("HEX line too short at line %d", line_num);
            valid = false;
            break;
        }
        
        // 检查是否都是十六进制字符
        for (int i = 1; i < len; i++) {
            if (!isxdigit(line[i])) {
                LOG_ERROR("Invalid hex character at line %d, position %d", line_num, i);
                valid = false;
                break;
            }
        }
    }
    
    fclose(file);
    return valid ? JTAG_SUCCESS : JTAG_ERROR_FILE_FORMAT;
}

static jtag_error_t hex_get_info(const char* filename, file_info_t* info) {
    if (!filename || !info) return JTAG_ERROR_INVALID_ARGS;
    
    FILE* file = fopen(filename, "r");
    if (!file) {
        return JTAG_ERROR_FILE_NOT_FOUND;
    }
    
    char line[256];
    uint32_t total_bytes = 0;
    uint32_t min_addr = 0xFFFFFFFF;
    uint32_t max_addr = 0;
    uint32_t entry_point = 0;
    bool has_entry = false;
    
    while (fgets(line, sizeof(line), file)) {
        if (line[0] != ':') continue;
        
        // 解析HEX记录
        unsigned int byte_count, address, record_type;
        if (sscanf(line + 1, "%02x%04x%02x", &byte_count, &address, &record_type) == 3) {
            if (record_type == 0x00) { // 数据记录
                total_bytes += byte_count;
                if (address < min_addr) min_addr = address;
                if (address + byte_count > max_addr) max_addr = address + byte_count;
            } else if (record_type == 0x05) { // 启动地址记录
                // 解析启动地址
                if (sscanf(line + 9, "%08x", &entry_point) == 1) {
                    has_entry = true;
                }
            }
        }
    }
    
    fclose(file);
    
    info->format = FILE_FORMAT_HEX;
    info->size = total_bytes;
    info->has_embedded_address = true;
    info->load_address = min_addr;
    info->entry_point = has_entry ? entry_point : min_addr;
    
    snprintf(info->description, sizeof(info->description), 
             "Intel HEX file, %u bytes, address range: 0x%08X-0x%08X", 
             total_bytes, min_addr, max_addr);
    
    return JTAG_SUCCESS;
}

static const char* hex_extensions[] = {"hex", NULL};

static file_format_handler_t hex_handler = {
    .format = FILE_FORMAT_HEX,
    .name = "Intel HEX",
    .extensions = hex_extensions,
    .detect = hex_detect,
    .validate = hex_validate,
    .get_info = hex_get_info,
    .requires_address = false,
    .has_embedded_address = true,
    .openocd_type_name = "ihex"
};

// ============================================================================
// BIT文件处理器 (Xilinx)
// ============================================================================

static jtag_error_t bit_detect(const char* filename) {
    if (!filename) return JTAG_ERROR_INVALID_ARGS;
    
    const char* ext = strrchr(filename, '.');
    if (ext && (strcasecmp(ext, ".bit") == 0)) {
        return JTAG_SUCCESS;
    }
    
    // 检查BIT文件头部
    FILE* file = fopen(filename, "rb");
    if (!file) return JTAG_ERROR_FILE_NOT_FOUND;
    
    unsigned char header[16];
    size_t read_bytes = fread(header, 1, sizeof(header), file);
    fclose(file);
    
    if (read_bytes >= 4) {
        // Xilinx BIT文件通常以特定的字节序列开始
        // 检查是否包含Xilinx特征
        if (header[0] == 0x00 && header[1] == 0x09) {
            return JTAG_SUCCESS;
        }
    }
    
    return JTAG_ERROR_FILE_FORMAT;
}

static jtag_error_t bit_validate(const char* filename) {
    FILE* file = fopen(filename, "rb");
    if (!file) {
        return JTAG_ERROR_FILE_NOT_FOUND;
    }
    
    // 检查文件大小
    fseek(file, 0, SEEK_END);
    long size = ftell(file);
    fclose(file);
    
    if (size < 100) { // BIT文件通常比较大
        return JTAG_ERROR_FILE_FORMAT;
    }
    
    return JTAG_SUCCESS;
}

static jtag_error_t bit_get_info(const char* filename, file_info_t* info) {
    if (!filename || !info) return JTAG_ERROR_INVALID_ARGS;
    
    FILE* file = fopen(filename, "rb");
    if (!file) {
        return JTAG_ERROR_FILE_NOT_FOUND;
    }
    
    fseek(file, 0, SEEK_END);
    info->size = ftell(file);
    fclose(file);
    
    info->format = FILE_FORMAT_BIT;
    info->has_embedded_address = false;
    info->entry_point = 0;
    info->load_address = 0;
    
    snprintf(info->description, sizeof(info->description), 
             "Xilinx bitstream file, %u bytes", info->size);
    
    return JTAG_SUCCESS;
}

static const char* bit_extensions[] = {"bit", NULL};

static file_format_handler_t bit_handler = {
    .format = FILE_FORMAT_BIT,
    .name = "Xilinx Bitstream",
    .extensions = bit_extensions,
    .detect = bit_detect,
    .validate = bit_validate,
    .get_info = bit_get_info,
    .requires_address = false,
    .has_embedded_address = false,
    .openocd_type_name = "bin"
};

// ============================================================================
// 文件格式管理器
// ============================================================================

static file_format_handler_t* registered_handlers[16];
static size_t handler_count = 0;

jtag_error_t file_format_manager_init(void) {
    handler_count = 0;
    
    // 注册内置处理器
    file_format_manager_register(&bin_handler);
    file_format_manager_register(&hex_handler);
    file_format_manager_register(&bit_handler);
    
    LOG_INFO("File format manager initialized with %zu handlers", handler_count);
    return JTAG_SUCCESS;
}

jtag_error_t file_format_manager_cleanup(void) {
    handler_count = 0;
    memset(registered_handlers, 0, sizeof(registered_handlers));
    return JTAG_SUCCESS;
}

jtag_error_t file_format_manager_register(const file_format_handler_t* handler) {
    if (!handler || handler_count >= sizeof(registered_handlers)/sizeof(registered_handlers[0])) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    registered_handlers[handler_count++] = (file_format_handler_t*)handler;
    LOG_DEBUG("Registered file format handler: %s", handler->name);
    return JTAG_SUCCESS;
}

file_format_handler_t* file_format_manager_get(file_format_t format) {
    for (size_t i = 0; i < handler_count; i++) {
        if (registered_handlers[i]->format == format) {
            return registered_handlers[i];
        }
    }
    return NULL;
}

file_format_handler_t* file_format_manager_get_by_extension(const char* filename) {
    if (!filename) return NULL;
    
    const char* ext = strrchr(filename, '.');
    if (!ext) return NULL;
    
    ext++; // 跳过'.'
    
    for (size_t i = 0; i < handler_count; i++) {
        const char** extensions = registered_handlers[i]->extensions;
        for (int j = 0; extensions[j]; j++) {
            if (strcasecmp(ext, extensions[j]) == 0) {
                return registered_handlers[i];
            }
        }
    }
    
    return NULL;
}

file_format_t file_format_detect(const char* filename) {
    if (!filename) return FILE_FORMAT_AUTO;
    
    // 首先尝试通过扩展名检测
    file_format_handler_t* handler = file_format_manager_get_by_extension(filename);
    if (handler) {
        // 验证文件内容
        if (handler->detect && handler->detect(filename) == JTAG_SUCCESS) {
            return handler->format;
        }
    }
    
    // 如果扩展名检测失败，尝试所有处理器
    for (size_t i = 0; i < handler_count; i++) {
        if (registered_handlers[i]->detect && 
            registered_handlers[i]->detect(filename) == JTAG_SUCCESS) {
            return registered_handlers[i]->format;
        }
    }
    
    return FILE_FORMAT_AUTO;
}

jtag_error_t file_format_get_info(const char* filename, file_info_t* info) {
    if (!filename || !info) return JTAG_ERROR_INVALID_ARGS;
    
    file_format_t format = file_format_detect(filename);
    if (format == FILE_FORMAT_AUTO) {
        return JTAG_ERROR_FILE_FORMAT;
    }
    
    file_format_handler_t* handler = file_format_manager_get(format);
    if (!handler || !handler->get_info) {
        return JTAG_ERROR_NOT_IMPLEMENTED;
    }
    
    return handler->get_info(filename, info);
}
